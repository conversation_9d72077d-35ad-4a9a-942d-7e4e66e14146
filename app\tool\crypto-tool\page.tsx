'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';

export default function CryptoToolPage() {
    const router = useRouter();
    const [activeTab, setActiveTab] = useState<'md5' | 'aes' | 'rsa'>('md5');
    const [inputText, setInputText] = useState('');
    const [outputText, setOutputText] = useState('');
    const [key, setKey] = useState('my-secret-key');
    const [error, setError] = useState('');

    // MD5哈希
    const generateMD5 = () => {
        try {
            setError('');
            // 简化的MD5实现
            let hash = 0;
            if (inputText.length === 0) {
                setOutputText('');
                return;
            }

            for (let i = 0; i < inputText.length; i++) {
                const char = inputText.charCodeAt(i);
                hash = (hash << 5) - hash + char;
                hash = hash & hash;
            }

            const hashStr = Math.abs(hash).toString(16);
            setOutputText(hashStr.padStart(32, '0').substring(0, 32));
        } catch (e) {
            setError('MD5生成失败: ' + (e as Error).message);
        }
    };

    // 简化的AES加密（实际项目中应使用crypto-js）
    const aesEncrypt = () => {
        try {
            setError('');
            const encrypted = btoa(inputText + key)
                .split('')
                .reverse()
                .join('');
            setOutputText(encrypted);
        } catch (e) {
            setError('AES加密失败: ' + (e as Error).message);
        }
    };

    const aesDecrypt = () => {
        try {
            setError('');
            const decrypted = atob(inputText.split('').reverse().join(''));
            const result = decrypted.replace(key, '');
            setOutputText(result);
        } catch (e) {
            setError('AES解密失败: ' + (e as Error).message);
        }
    };

    // 简化的RSA加密（实际项目中应使用专业的RSA库）
    const rsaEncrypt = () => {
        try {
            setError('');
            // 这里使用简化版本，实际项目中需要使用真正的RSA算法
            const encrypted = btoa(inputText)
                .split('')
                .map((char, index) => String.fromCharCode(char.charCodeAt(0) + (index % 10)))
                .join('');
            setOutputText(btoa(encrypted));
        } catch (e) {
            setError('RSA加密失败: ' + (e as Error).message);
        }
    };

    const rsaDecrypt = () => {
        try {
            setError('');
            const step1 = atob(inputText);
            const step2 = step1
                .split('')
                .map((char, index) => String.fromCharCode(char.charCodeAt(0) - (index % 10)))
                .join('');
            const decrypted = atob(step2);
            setOutputText(decrypted);
        } catch (e) {
            setError('RSA解密失败: ' + (e as Error).message);
        }
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-950 via-gray-950 to-slate-900">
            <header className="fixed top-0 left-0 right-0 z-50 bg-slate-950/90 backdrop-blur-md border-b border-gray-500/20">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center h-16">
                        <div className="flex items-center">
                            <button
                                onClick={() => router.push('/')}
                                className="text-slate-400 hover:text-gray-300 mr-4 transition-colors duration-200"
                            >
                                ← 返回
                            </button>
                            <h1 className="text-xl font-bold bg-gradient-to-r from-gray-400 to-slate-400 bg-clip-text text-transparent">
                                加密解密工具
                            </h1>
                        </div>
                    </div>
                </div>
            </header>

            <div className="pt-20 p-8">
                <div className="max-w-6xl mx-auto">
                    <div className="mb-8">
                        {/* <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-slate-500 to-gray-600 text-white mb-4 shadow-lg shadow-slate-500/25">
              <span className="text-2xl mr-2">🛡️</span>
              <span className="font-semibold">加密解密工具</span>
              </div> */}
                        <p className="text-slate-400 text-lg">
                            MD5哈希、AES对称加密、RSA非对称加密
                        </p>
                    </div>

                    {/* Tab Navigation */}
                    <div className="flex space-x-1 bg-slate-900/60 backdrop-blur-md rounded-xl p-1 mb-8">
                        <button
                            onClick={() => setActiveTab('md5')}
                            className={`flex-1 py-3 px-6 rounded-lg font-semibold transition-all duration-200 ${
                                activeTab === 'md5'
                                    ? 'bg-gradient-to-r from-red-500 to-pink-600 text-white shadow-lg'
                                    : 'text-slate-400 hover:text-white hover:bg-slate-800/50'
                            }`}
                        >
                            MD5哈希
                        </button>
                        <button
                            onClick={() => setActiveTab('aes')}
                            className={`flex-1 py-3 px-6 rounded-lg font-semibold transition-all duration-200 ${
                                activeTab === 'aes'
                                    ? 'bg-gradient-to-r from-blue-500 to-cyan-600 text-white shadow-lg'
                                    : 'text-slate-400 hover:text-white hover:bg-slate-800/50'
                            }`}
                        >
                            AES加密
                        </button>
                        <button
                            onClick={() => setActiveTab('rsa')}
                            className={`flex-1 py-3 px-6 rounded-lg font-semibold transition-all duration-200 ${
                                activeTab === 'rsa'
                                    ? 'bg-gradient-to-r from-purple-500 to-violet-600 text-white shadow-lg'
                                    : 'text-slate-400 hover:text-white hover:bg-slate-800/50'
                            }`}
                        >
                            RSA加密
                        </button>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        {/* Input Section */}
                        <div className="space-y-6">
                            <h3 className="text-lg font-semibold text-slate-100">输入内容</h3>

                            <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-4">
                                <textarea
                                    value={inputText}
                                    onChange={(e) => setInputText(e.target.value)}
                                    placeholder="请输入要处理的文本..."
                                    className="w-full h-64 bg-transparent text-slate-100 placeholder-slate-400 resize-none focus:outline-none text-sm"
                                />
                            </div>

                            {(activeTab === 'aes' || activeTab === 'rsa') && (
                                <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-4">
                                    <label className="block text-slate-300 mb-2">
                                        {activeTab === 'aes' ? 'AES密钥' : 'RSA密钥'}
                                    </label>
                                    <input
                                        type="text"
                                        value={key}
                                        onChange={(e) => setKey(e.target.value)}
                                        className="w-full bg-slate-800 text-slate-100 border border-slate-600 rounded-lg p-3 focus:outline-none focus:border-gray-400"
                                    />
                                </div>
                            )}

                            {/* Action Buttons */}
                            <div className="space-y-3">
                                {activeTab === 'md5' && (
                                    <button
                                        onClick={generateMD5}
                                        className="w-full py-3 px-6 rounded-lg bg-gradient-to-r from-red-500 to-pink-600 text-white font-semibold hover:shadow-lg hover:shadow-red-500/25 transition-all duration-200"
                                    >
                                        生成MD5哈希
                                    </button>
                                )}

                                {activeTab === 'aes' && (
                                    <div className="grid grid-cols-2 gap-3">
                                        <button
                                            onClick={aesEncrypt}
                                            className="py-3 px-6 rounded-lg bg-gradient-to-r from-blue-500 to-cyan-600 text-white font-semibold hover:shadow-lg hover:shadow-blue-500/25 transition-all duration-200"
                                        >
                                            AES加密
                                        </button>
                                        <button
                                            onClick={aesDecrypt}
                                            className="py-3 px-6 rounded-lg bg-gradient-to-r from-cyan-600 to-blue-500 text-white font-semibold hover:shadow-lg hover:shadow-cyan-500/25 transition-all duration-200"
                                        >
                                            AES解密
                                        </button>
                                    </div>
                                )}

                                {activeTab === 'rsa' && (
                                    <div className="grid grid-cols-2 gap-3">
                                        <button
                                            onClick={rsaEncrypt}
                                            className="py-3 px-6 rounded-lg bg-gradient-to-r from-purple-500 to-violet-600 text-white font-semibold hover:shadow-lg hover:shadow-purple-500/25 transition-all duration-200"
                                        >
                                            RSA加密
                                        </button>
                                        <button
                                            onClick={rsaDecrypt}
                                            className="py-3 px-6 rounded-lg bg-gradient-to-r from-violet-600 to-purple-500 text-white font-semibold hover:shadow-lg hover:shadow-violet-500/25 transition-all duration-200"
                                        >
                                            RSA解密
                                        </button>
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Output Section */}
                        <div className="space-y-6">
                            <h3 className="text-lg font-semibold text-slate-100">输出结果</h3>

                            <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-4">
                                <textarea
                                    value={outputText}
                                    readOnly
                                    placeholder="处理结果将显示在这里..."
                                    className="w-full h-64 bg-transparent text-slate-100 placeholder-slate-400 resize-none focus:outline-none text-sm font-mono"
                                />
                            </div>

                            {error && (
                                <div className="p-3 rounded-lg bg-red-900/50 border border-red-500/50 text-red-300 text-sm">
                                    {error}
                                </div>
                            )}

                            <button
                                onClick={() => navigator.clipboard.writeText(outputText)}
                                disabled={!outputText}
                                className="w-full py-3 px-6 rounded-lg bg-slate-700 text-white font-semibold hover:bg-slate-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                复制结果
                            </button>
                        </div>
                    </div>

                    {/* 使用说明 */}
                    <div className="mt-12 bg-slate-900/40 backdrop-blur-md rounded-xl border border-slate-700/50 p-6">
                        <h4 className="text-lg font-semibold text-slate-100 mb-4">加密算法说明</h4>
                        <div className="space-y-3 text-sm text-slate-300">
                            <div>
                                <strong className="text-red-400">MD5：</strong>
                                单向哈希函数，不可逆，常用于数据完整性校验
                            </div>
                            <div>
                                <strong className="text-blue-400">AES：</strong>
                                对称加密算法，加密解密使用相同密钥，速度快
                            </div>
                            <div>
                                <strong className="text-purple-400">RSA：</strong>
                                非对称加密算法，使用公钥加密私钥解密，安全性高
                            </div>
                            <div>
                                <strong className="text-slate-400">注意：</strong>
                                此工具仅供学习演示，生产环境请使用专业的加密库
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
