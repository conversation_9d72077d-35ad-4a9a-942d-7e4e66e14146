'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

interface ColorValues {
    hex: string;
    rgb: { r: number; g: number; b: number };
    hsl: { h: number; s: number; l: number };
    hsv: { h: number; s: number; v: number };
}

export default function ColorConverterPage() {
    const router = useRouter();
    const [colorValues, setColorValues] = useState<ColorValues>({
        hex: '#3B82F6',
        rgb: { r: 59, g: 130, b: 246 },
        hsl: { h: 217, s: 91, l: 60 },
        hsv: { h: 217, s: 76, v: 96 },
    });

    // 颜色转换函数
    const hexToRgb = (hex: string): { r: number; g: number; b: number } | null => {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result
            ? {
                  r: parseInt(result[1], 16),
                  g: parseInt(result[2], 16),
                  b: parseInt(result[3], 16),
              }
            : null;
    };

    const rgbToHex = (r: number, g: number, b: number): string => {
        return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
    };

    const rgbToHsl = (r: number, g: number, b: number): { h: number; s: number; l: number } => {
        r /= 255;
        g /= 255;
        b /= 255;
        const max = Math.max(r, g, b);
        const min = Math.min(r, g, b);
        let h = 0,
            s = 0,
            l = (max + min) / 2;

        if (max === min) {
            h = s = 0;
        } else {
            const d = max - min;
            s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
            switch (max) {
                case r:
                    h = (g - b) / d + (g < b ? 6 : 0);
                    break;
                case g:
                    h = (b - r) / d + 2;
                    break;
                case b:
                    h = (r - g) / d + 4;
                    break;
            }
            h /= 6;
        }

        return {
            h: Math.round(h * 360),
            s: Math.round(s * 100),
            l: Math.round(l * 100),
        };
    };

    const rgbToHsv = (r: number, g: number, b: number): { h: number; s: number; v: number } => {
        r /= 255;
        g /= 255;
        b /= 255;
        const max = Math.max(r, g, b);
        const min = Math.min(r, g, b);
        let h = 0,
            s = 0,
            v = max;

        const d = max - min;
        s = max === 0 ? 0 : d / max;

        if (max === min) {
            h = 0;
        } else {
            switch (max) {
                case r:
                    h = (g - b) / d + (g < b ? 6 : 0);
                    break;
                case g:
                    h = (b - r) / d + 2;
                    break;
                case b:
                    h = (r - g) / d + 4;
                    break;
            }
            h /= 6;
        }

        return {
            h: Math.round(h * 360),
            s: Math.round(s * 100),
            v: Math.round(v * 100),
        };
    };

    const updateFromHex = (hex: string) => {
        const rgb = hexToRgb(hex);
        if (rgb) {
            const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b);
            const hsv = rgbToHsv(rgb.r, rgb.g, rgb.b);
            setColorValues({ hex, rgb, hsl, hsv });
        }
    };

    const updateFromRgb = (r: number, g: number, b: number) => {
        const hex = rgbToHex(r, g, b);
        const hsl = rgbToHsl(r, g, b);
        const hsv = rgbToHsv(r, g, b);
        setColorValues({ hex, rgb: { r, g, b }, hsl, hsv });
    };

    const generateRandomColor = () => {
        const r = Math.floor(Math.random() * 256);
        const g = Math.floor(Math.random() * 256);
        const b = Math.floor(Math.random() * 256);
        updateFromRgb(r, g, b);
    };

    const presetColors = [
        '#FF0000',
        '#00FF00',
        '#0000FF',
        '#FFFF00',
        '#FF00FF',
        '#00FFFF',
        '#FFA500',
        '#800080',
        '#FFC0CB',
        '#A52A2A',
        '#808080',
        '#000000',
    ];

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-950 via-fuchsia-950 to-pink-950">
            {/* Fixed Header */}
            <header className="fixed top-0 left-0 right-0 z-50 bg-slate-950/90 backdrop-blur-md border-b border-fuchsia-500/20">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center h-16">
                        <div className="flex items-center">
                            <button
                                onClick={() => router.push('/')}
                                className="text-slate-400 hover:text-fuchsia-300 mr-4 transition-colors duration-200"
                            >
                                ← 返回
                            </button>
                            <h1 className="text-xl font-bold bg-gradient-to-r from-fuchsia-400 to-pink-400 bg-clip-text text-transparent">
                                颜色转换工具
                            </h1>
                        </div>
                    </div>
                </div>
            </header>

            <div className="pt-20 p-8">
                <div className="max-w-6xl mx-auto">
                    {/* Tool Header */}
                    <div className="mb-8">
                        <p className="text-slate-400 text-lg">RGB、HEX、HSL、HSV颜色格式互相转换</p>
                    </div>

                    {/* Color Preview */}
                    <div className="mb-8 bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-6">
                        <h3 className="text-lg font-semibold text-slate-100 mb-4">颜色预览</h3>
                        <div className="flex items-center gap-6">
                            <div
                                className="w-32 h-32 rounded-xl shadow-lg border-2 border-slate-600"
                                style={{ backgroundColor: colorValues.hex }}
                            ></div>
                            <div className="flex-1">
                                <div className="text-2xl font-bold text-slate-100 mb-2">
                                    {colorValues.hex}
                                </div>
                                <div className="text-slate-400">
                                    RGB({colorValues.rgb.r}, {colorValues.rgb.g},{' '}
                                    {colorValues.rgb.b})
                                </div>
                                <div className="text-slate-400">
                                    HSL({colorValues.hsl.h}°, {colorValues.hsl.s}%,{' '}
                                    {colorValues.hsl.l}%)
                                </div>
                                <div className="text-slate-400">
                                    HSV({colorValues.hsv.h}°, {colorValues.hsv.s}%,{' '}
                                    {colorValues.hsv.v}%)
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        {/* Input Section */}
                        <div className="space-y-6">
                            <h3 className="text-lg font-semibold text-slate-100">颜色输入</h3>

                            {/* HEX Input */}
                            <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-4">
                                <label className="block text-slate-300 mb-2">HEX颜色值</label>
                                <input
                                    type="text"
                                    value={colorValues.hex}
                                    onChange={(e) => updateFromHex(e.target.value)}
                                    className="w-full bg-slate-800 text-slate-100 border border-slate-600 rounded-lg p-3 focus:outline-none focus:border-fuchsia-400 font-mono"
                                    placeholder="#FF0000"
                                />
                            </div>

                            {/* RGB Input */}
                            <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-4">
                                <label className="block text-slate-300 mb-3">RGB颜色值</label>
                                <div className="grid grid-cols-3 gap-3">
                                    <div>
                                        <label className="block text-xs text-slate-400 mb-1">
                                            R (0-255)
                                        </label>
                                        <input
                                            type="number"
                                            min="0"
                                            max="255"
                                            value={colorValues.rgb.r}
                                            onChange={(e) =>
                                                updateFromRgb(
                                                    parseInt(e.target.value) || 0,
                                                    colorValues.rgb.g,
                                                    colorValues.rgb.b,
                                                )
                                            }
                                            className="w-full bg-slate-800 text-slate-100 border border-slate-600 rounded-lg p-2 focus:outline-none focus:border-fuchsia-400"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-xs text-slate-400 mb-1">
                                            G (0-255)
                                        </label>
                                        <input
                                            type="number"
                                            min="0"
                                            max="255"
                                            value={colorValues.rgb.g}
                                            onChange={(e) =>
                                                updateFromRgb(
                                                    colorValues.rgb.r,
                                                    parseInt(e.target.value) || 0,
                                                    colorValues.rgb.b,
                                                )
                                            }
                                            className="w-full bg-slate-800 text-slate-100 border border-slate-600 rounded-lg p-2 focus:outline-none focus:border-fuchsia-400"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-xs text-slate-400 mb-1">
                                            B (0-255)
                                        </label>
                                        <input
                                            type="number"
                                            min="0"
                                            max="255"
                                            value={colorValues.rgb.b}
                                            onChange={(e) =>
                                                updateFromRgb(
                                                    colorValues.rgb.r,
                                                    colorValues.rgb.g,
                                                    parseInt(e.target.value) || 0,
                                                )
                                            }
                                            className="w-full bg-slate-800 text-slate-100 border border-slate-600 rounded-lg p-2 focus:outline-none focus:border-fuchsia-400"
                                        />
                                    </div>
                                </div>
                            </div>

                            {/* Color Picker */}
                            <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-4">
                                <label className="block text-slate-300 mb-2">颜色选择器</label>
                                <input
                                    type="color"
                                    value={colorValues.hex}
                                    onChange={(e) => updateFromHex(e.target.value)}
                                    className="w-full h-12 rounded-lg cursor-pointer"
                                />
                            </div>

                            <button
                                onClick={generateRandomColor}
                                className="w-full py-3 px-6 rounded-lg bg-gradient-to-r from-fuchsia-400 to-pink-500 text-white font-semibold hover:shadow-lg hover:shadow-fuchsia-500/25 transition-all duration-200"
                            >
                                随机颜色
                            </button>
                        </div>

                        {/* Output Section */}
                        <div className="space-y-6">
                            <h3 className="text-lg font-semibold text-slate-100">转换结果</h3>

                            {/* Format Outputs */}
                            <div className="space-y-4">
                                <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-4">
                                    <label className="block text-slate-300 mb-2">HEX</label>
                                    <div className="flex items-center gap-3">
                                        <code className="flex-1 bg-slate-800 text-slate-100 p-3 rounded-lg font-mono">
                                            {colorValues.hex}
                                        </code>
                                        <button
                                            onClick={() =>
                                                navigator.clipboard.writeText(colorValues.hex)
                                            }
                                            className="px-3 py-2 bg-fuchsia-600 text-white rounded-lg hover:bg-fuchsia-500 transition-colors"
                                        >
                                            复制
                                        </button>
                                    </div>
                                </div>

                                <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-4">
                                    <label className="block text-slate-300 mb-2">RGB</label>
                                    <div className="flex items-center gap-3">
                                        <code className="flex-1 bg-slate-800 text-slate-100 p-3 rounded-lg font-mono">
                                            rgb({colorValues.rgb.r}, {colorValues.rgb.g},{' '}
                                            {colorValues.rgb.b})
                                        </code>
                                        <button
                                            onClick={() =>
                                                navigator.clipboard.writeText(
                                                    `rgb(${colorValues.rgb.r}, ${colorValues.rgb.g}, ${colorValues.rgb.b})`,
                                                )
                                            }
                                            className="px-3 py-2 bg-fuchsia-600 text-white rounded-lg hover:bg-fuchsia-500 transition-colors"
                                        >
                                            复制
                                        </button>
                                    </div>
                                </div>

                                <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-4">
                                    <label className="block text-slate-300 mb-2">HSL</label>
                                    <div className="flex items-center gap-3">
                                        <code className="flex-1 bg-slate-800 text-slate-100 p-3 rounded-lg font-mono">
                                            hsl({colorValues.hsl.h}, {colorValues.hsl.s}%,{' '}
                                            {colorValues.hsl.l}%)
                                        </code>
                                        <button
                                            onClick={() =>
                                                navigator.clipboard.writeText(
                                                    `hsl(${colorValues.hsl.h}, ${colorValues.hsl.s}%, ${colorValues.hsl.l}%)`,
                                                )
                                            }
                                            className="px-3 py-2 bg-fuchsia-600 text-white rounded-lg hover:bg-fuchsia-500 transition-colors"
                                        >
                                            复制
                                        </button>
                                    </div>
                                </div>

                                <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-4">
                                    <label className="block text-slate-300 mb-2">HSV</label>
                                    <div className="flex items-center gap-3">
                                        <code className="flex-1 bg-slate-800 text-slate-100 p-3 rounded-lg font-mono">
                                            hsv({colorValues.hsv.h}, {colorValues.hsv.s}%,{' '}
                                            {colorValues.hsv.v}%)
                                        </code>
                                        <button
                                            onClick={() =>
                                                navigator.clipboard.writeText(
                                                    `hsv(${colorValues.hsv.h}, ${colorValues.hsv.s}%, ${colorValues.hsv.v}%)`,
                                                )
                                            }
                                            className="px-3 py-2 bg-fuchsia-600 text-white rounded-lg hover:bg-fuchsia-500 transition-colors"
                                        >
                                            复制
                                        </button>
                                    </div>
                                </div>
                            </div>

                            {/* Preset Colors */}
                            <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-4">
                                <label className="block text-slate-300 mb-3">预设颜色</label>
                                <div className="grid grid-cols-6 gap-2">
                                    {presetColors.map((color, index) => (
                                        <button
                                            key={index}
                                            onClick={() => updateFromHex(color)}
                                            className="w-8 h-8 rounded-lg border-2 border-slate-600 hover:border-slate-400 transition-colors"
                                            style={{ backgroundColor: color }}
                                            title={color}
                                        />
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Usage Examples */}
                    <div className="mt-12 bg-slate-900/40 backdrop-blur-md rounded-xl border border-slate-700/50 p-6">
                        <h4 className="text-lg font-semibold text-slate-100 mb-4">颜色格式说明</h4>
                        <div className="space-y-3 text-sm text-slate-300">
                            <div>
                                <strong className="text-fuchsia-400">HEX：</strong>
                                十六进制颜色码，如 #FF0000，常用于CSS和设计
                            </div>
                            <div>
                                <strong className="text-pink-400">RGB：</strong>
                                红绿蓝三原色值，每个值范围0-255
                            </div>
                            <div>
                                <strong className="text-fuchsia-300">HSL：</strong>
                                色相、饱和度、亮度，更直观的颜色表示方法
                            </div>
                            <div>
                                <strong className="text-pink-300">HSV：</strong>
                                色相、饱和度、明度，常用于颜色选择器
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
