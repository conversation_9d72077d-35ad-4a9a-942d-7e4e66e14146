'use client';

import { useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import ToolSidebar from '../../../components/ToolSidebar';

const toolsData = {
    'json-formatter': {
        name: 'JSON格式化',
        desc: 'JSON数据格式化和验证',
        icon: '{}',
        color: 'from-blue-500 to-cyan-500',
    },
    'url-encoder': {
        name: 'URL编码解码',
        desc: 'URL参数编码和解码转换',
        icon: '🔗',
        color: 'from-green-500 to-emerald-500',
    },
    'base64-encoder': {
        name: 'Base64编解码',
        desc: 'Base64格式编码解码',
        icon: '📝',
        color: 'from-purple-500 to-violet-500',
    },
    'md5-hash': {
        name: 'MD5加密',
        desc: 'MD5哈希值生成',
        icon: '🔐',
        color: 'from-red-500 to-pink-500',
    },
    'uuid-generator': {
        name: 'UUID生成器',
        desc: '生成唯一标识符',
        icon: '🆔',
        color: 'from-yellow-500 to-orange-500',
    },
    'timestamp-converter': {
        name: '时间戳转换',
        desc: '时间戳与日期互转',
        icon: '⏰',
        color: 'from-indigo-500 to-blue-500',
    },
    'qr-generator': {
        name: '二维码生成',
        desc: '文本转二维码',
        icon: '📱',
        color: 'from-teal-500 to-cyan-500',
    },
    'color-converter': {
        name: '颜色转换',
        desc: 'RGB、HEX颜色转换',
        icon: '🎨',
        color: 'from-pink-500 to-rose-500',
    },
};

export default function ToolPage() {
    const params = useParams();
    const router = useRouter();
    const toolName = params.toolName as string;
    const [inputValue, setInputValue] = useState('');
    const [outputValue, setOutputValue] = useState('');

    const currentTool = toolsData[toolName as keyof typeof toolsData];
    const allTools = Object.entries(toolsData);

    if (!currentTool) {
        return <div>工具不存在</div>;
    }

    const handleProcess = () => {
        switch (toolName) {
            case 'json-formatter':
                try {
                    const parsed = JSON.parse(inputValue);
                    setOutputValue(JSON.stringify(parsed, null, 2));
                } catch (e) {
                    setOutputValue('JSON格式错误');
                }
                break;
            case 'url-encoder':
                setOutputValue(encodeURIComponent(inputValue));
                break;
            case 'base64-encoder':
                setOutputValue(btoa(inputValue));
                break;
            case 'md5-hash':
                // 简单示例，实际项目中需要引入MD5库
                setOutputValue('MD5哈希值: ' + inputValue.length.toString(16));
                break;
            case 'uuid-generator':
                setOutputValue(crypto.randomUUID());
                break;
            case 'timestamp-converter':
                const timestamp = parseInt(inputValue);
                if (!isNaN(timestamp)) {
                    setOutputValue(new Date(timestamp * 1000).toLocaleString());
                } else {
                    setOutputValue(Math.floor(new Date(inputValue).getTime() / 1000).toString());
                }
                break;
            default:
                setOutputValue('处理完成: ' + inputValue);
        }
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
            {/* Fixed Header */}
            <header className="fixed top-0 left-0 right-0 z-50 bg-gray-900/95 backdrop-blur-sm border-b border-gray-700">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center h-16">
                        <div className="flex items-center">
                            <button
                                onClick={() => router.push('/')}
                                className="text-gray-400 hover:text-white mr-4 transition-colors"
                            >
                                ← 返回
                            </button>
                            <h1 className="text-xl font-bold text-white">开发工具平台</h1>
                        </div>
                        <div className="flex items-center space-x-4">
                            <span className="text-sm text-gray-400">v1.0.0</span>
                        </div>
                    </div>
                </div>
            </header>

            {/* 使用统一的侧边栏组件 */}
            <ToolSidebar currentTool={toolName} />

            {/* Main Content */}
            <div className="flex-1 p-8">
                <div className="max-w-4xl mx-auto">
                    {/* Tool Header */}
                    <div className="mb-8">
                        <div
                            className={`inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r ${currentTool.color} text-white mb-4`}
                        >
                            <span className="text-2xl mr-2">{currentTool.icon}</span>
                            <span className="font-semibold">{currentTool.name}</span>
                        </div>
                        <p className="text-gray-400 text-lg">{currentTool.desc}</p>
                    </div>

                    {/* Tool Interface */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        {/* Input Section */}
                        <div className="space-y-4">
                            <h3 className="text-lg font-semibold text-white">输入</h3>
                            <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg border border-gray-700 p-4">
                                <textarea
                                    value={inputValue}
                                    onChange={(e) => setInputValue(e.target.value)}
                                    placeholder="请输入要处理的内容..."
                                    className="w-full h-64 bg-transparent text-white placeholder-gray-400 resize-none focus:outline-none"
                                />
                            </div>
                            <button
                                onClick={handleProcess}
                                className={`w-full py-3 px-6 rounded-lg bg-gradient-to-r ${currentTool.color} text-white font-semibold hover:shadow-lg transition-all duration-200`}
                            >
                                处理
                            </button>
                        </div>

                        {/* Output Section */}
                        <div className="space-y-4">
                            <h3 className="text-lg font-semibold text-white">输出</h3>
                            <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg border border-gray-700 p-4">
                                <textarea
                                    value={outputValue}
                                    readOnly
                                    placeholder="处理结果将显示在这里..."
                                    className="w-full h-64 bg-transparent text-white placeholder-gray-400 resize-none focus:outline-none"
                                />
                            </div>
                            <button
                                onClick={() => navigator.clipboard.writeText(outputValue)}
                                className="w-full py-3 px-6 rounded-lg bg-gray-700 text-white font-semibold hover:bg-gray-600 transition-colors"
                            >
                                复制结果
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}

