'use client';

import { useState, useRef } from 'react';
import { useRouter } from 'next/navigation';

export default function ImageCompressPage() {
    const router = useRouter();
    const fileInputRef = useRef<HTMLInputElement>(null);
    const [originalImage, setOriginalImage] = useState<File | null>(null);
    const [compressedImage, setCompressedImage] = useState<string | null>(null);
    const [originalSize, setOriginalSize] = useState<number>(0);
    const [compressedSize, setCompressedSize] = useState<number>(0);
    const [quality, setQuality] = useState<number>(80);
    const [maxWidth, setMaxWidth] = useState<number>(1920);
    const [maxHeight, setMaxHeight] = useState<number>(1080);
    const [isCompressing, setIsCompressing] = useState<boolean>(false);

    const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file && file.type.startsWith('image/')) {
            setOriginalImage(file);
            setOriginalSize(file.size);
            setCompressedImage(null);
            setCompressedSize(0);
        }
    };

    const compressImage = async () => {
        if (!originalImage) return;

        setIsCompressing(true);

        try {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();

            img.onload = () => {
                // 计算新的尺寸
                let { width, height } = img;

                if (width > maxWidth || height > maxHeight) {
                    const ratio = Math.min(maxWidth / width, maxHeight / height);
                    width *= ratio;
                    height *= ratio;
                }

                canvas.width = width;
                canvas.height = height;

                // 绘制图片
                ctx?.drawImage(img, 0, 0, width, height);

                // 压缩并转换为blob
                canvas.toBlob(
                    (blob) => {
                        if (blob) {
                            const url = URL.createObjectURL(blob);
                            setCompressedImage(url);
                            setCompressedSize(blob.size);
                        }
                        setIsCompressing(false);
                    },
                    'image/jpeg',
                    quality / 100,
                );
            };

            img.src = URL.createObjectURL(originalImage);
        } catch (error) {
            console.error('压缩失败:', error);
            setIsCompressing(false);
        }
    };

    const downloadCompressed = () => {
        if (!compressedImage) return;

        const link = document.createElement('a');
        link.href = compressedImage;
        link.download = `compressed_${originalImage?.name || 'image.jpg'}`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const formatFileSize = (bytes: number): string => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    const compressionRatio =
        originalSize > 0 ? ((originalSize - compressedSize) / originalSize) * 100 : 0;

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-950 via-emerald-950 to-green-950">
            <header className="fixed top-0 left-0 right-0 z-50 bg-slate-950/90 backdrop-blur-md border-b border-emerald-500/20">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center h-16">
                        <div className="flex items-center">
                            <button
                                onClick={() => router.push('/')}
                                className="text-slate-400 hover:text-emerald-300 mr-4 transition-colors duration-200"
                            >
                                ← 返回
                            </button>
                            <h1 className="text-xl font-bold bg-gradient-to-r from-emerald-400 to-green-400 bg-clip-text text-transparent">
                                图片压缩工具
                            </h1>
                        </div>
                    </div>
                </div>
            </header>

            <div className="pt-20 p-8">
                <div className="max-w-6xl mx-auto">
                    <div className="mb-8">
                        {/* <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-emerald-400 to-green-500 text-white mb-4 shadow-lg shadow-emerald-500/25">
              <span className="text-2xl mr-2">🖼️</span>
              <span className="font-semibold">图片压缩优化</span>
              </div> */}
                        <p className="text-slate-400 text-lg">
                            在线图片压缩，减小文件大小，保持画质
                        </p>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        {/* 上传和设置区域 */}
                        <div className="space-y-6">
                            <h3 className="text-lg font-semibold text-slate-100">图片上传</h3>

                            {/* 文件上传 */}
                            <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-6">
                                <input
                                    ref={fileInputRef}
                                    type="file"
                                    accept="image/*"
                                    onChange={handleFileSelect}
                                    className="hidden"
                                />

                                <div
                                    onClick={() => fileInputRef.current?.click()}
                                    className="border-2 border-dashed border-slate-600 rounded-lg p-8 text-center cursor-pointer hover:border-emerald-500 transition-colors"
                                >
                                    <div className="text-4xl mb-4">📁</div>
                                    <p className="text-slate-300 mb-2">点击选择图片文件</p>
                                    <p className="text-slate-500 text-sm">
                                        支持 JPG, PNG, WebP 格式
                                    </p>
                                </div>
                            </div>

                            {originalImage && (
                                <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-4">
                                    <h4 className="text-slate-300 mb-3">原图信息</h4>
                                    <div className="space-y-2 text-sm">
                                        <div className="flex justify-between">
                                            <span className="text-slate-400">文件名:</span>
                                            <span className="text-slate-200">
                                                {originalImage.name}
                                            </span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-slate-400">文件大小:</span>
                                            <span className="text-slate-200">
                                                {formatFileSize(originalSize)}
                                            </span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-slate-400">文件类型:</span>
                                            <span className="text-slate-200">
                                                {originalImage.type}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            )}

                            {/* 压缩设置 */}
                            <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-4 space-y-4">
                                <h4 className="text-slate-300">压缩设置</h4>

                                <div>
                                    <label className="block text-slate-400 mb-2">
                                        图片质量: {quality}%
                                    </label>
                                    <input
                                        type="range"
                                        min="10"
                                        max="100"
                                        value={quality}
                                        onChange={(e) => setQuality(parseInt(e.target.value))}
                                        className="w-full h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer"
                                    />
                                </div>

                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <label className="block text-slate-400 mb-2">
                                            最大宽度
                                        </label>
                                        <input
                                            type="number"
                                            value={maxWidth}
                                            onChange={(e) => setMaxWidth(parseInt(e.target.value))}
                                            className="w-full bg-slate-800 text-slate-100 border border-slate-600 rounded-lg p-2 focus:outline-none focus:border-emerald-400"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-slate-400 mb-2">
                                            最大高度
                                        </label>
                                        <input
                                            type="number"
                                            value={maxHeight}
                                            onChange={(e) => setMaxHeight(parseInt(e.target.value))}
                                            className="w-full bg-slate-800 text-slate-100 border border-slate-600 rounded-lg p-2 focus:outline-none focus:border-emerald-400"
                                        />
                                    </div>
                                </div>
                            </div>

                            <button
                                onClick={compressImage}
                                disabled={!originalImage || isCompressing}
                                className="w-full py-3 px-6 rounded-lg bg-gradient-to-r from-emerald-500 to-green-600 text-white font-semibold hover:shadow-lg hover:shadow-emerald-500/25 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                {isCompressing ? '压缩中...' : '开始压缩'}
                            </button>
                        </div>

                        {/* 预览和结果区域 */}
                        <div className="space-y-6">
                            <h3 className="text-lg font-semibold text-slate-100">压缩结果</h3>

                            {compressedImage && (
                                <>
                                    <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-4">
                                        <h4 className="text-slate-300 mb-3">压缩统计</h4>
                                        <div className="grid grid-cols-2 gap-4 text-sm">
                                            <div className="text-center p-3 bg-slate-800/50 rounded-lg">
                                                <div className="text-slate-400">原始大小</div>
                                                <div className="text-slate-200 font-semibold">
                                                    {formatFileSize(originalSize)}
                                                </div>
                                            </div>
                                            <div className="text-center p-3 bg-slate-800/50 rounded-lg">
                                                <div className="text-slate-400">压缩后</div>
                                                <div className="text-emerald-400 font-semibold">
                                                    {formatFileSize(compressedSize)}
                                                </div>
                                            </div>
                                        </div>
                                        <div className="mt-4 text-center">
                                            <div className="text-slate-400 text-sm">压缩率</div>
                                            <div className="text-green-400 text-xl font-bold">
                                                {compressionRatio.toFixed(1)}%
                                            </div>
                                        </div>
                                    </div>

                                    <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-4">
                                        <h4 className="text-slate-300 mb-3">压缩预览</h4>
                                        <div className="text-center">
                                            <img
                                                src={compressedImage}
                                                alt="压缩后的图片"
                                                className="max-w-full max-h-64 mx-auto rounded-lg shadow-lg"
                                            />
                                        </div>
                                    </div>

                                    <button
                                        onClick={downloadCompressed}
                                        className="w-full py-3 px-6 rounded-lg bg-gradient-to-r from-green-600 to-emerald-500 text-white font-semibold hover:shadow-lg hover:shadow-green-500/25 transition-all duration-200"
                                    >
                                        下载压缩图片
                                    </button>
                                </>
                            )}

                            {!compressedImage && !originalImage && (
                                <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-8 text-center">
                                    <div className="text-6xl mb-4">🖼️</div>
                                    <p className="text-slate-400">请先上传图片文件</p>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* 使用说明 */}
                    <div className="mt-12 bg-slate-900/40 backdrop-blur-md rounded-xl border border-slate-700/50 p-6">
                        <h4 className="text-lg font-semibold text-slate-100 mb-4">使用说明</h4>
                        <div className="space-y-3 text-sm text-slate-300">
                            <div>
                                <strong className="text-emerald-400">支持格式：</strong>
                                JPG、PNG、WebP等常见图片格式
                            </div>
                            <div>
                                <strong className="text-green-400">压缩原理：</strong>
                                通过调整图片质量和尺寸来减小文件大小
                            </div>
                            <div>
                                <strong className="text-emerald-300">质量设置：</strong>
                                质量越低文件越小，但画质会下降
                            </div>
                            <div>
                                <strong className="text-green-300">尺寸限制：</strong>
                                超过设定尺寸的图片会按比例缩放
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
