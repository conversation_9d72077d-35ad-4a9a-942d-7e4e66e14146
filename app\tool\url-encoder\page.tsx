'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';

export default function UrlEncoderPage() {
    const router = useRouter();
    const [inputValue, setInputValue] = useState('');
    const [outputValue, setOutputValue] = useState('');

    const encodeUrl = () => {
        try {
            setOutputValue(encodeURIComponent(inputValue));
        } catch (e) {
            setOutputValue('编码失败');
        }
    };

    const decodeUrl = () => {
        try {
            setOutputValue(decodeURIComponent(inputValue));
        } catch (e) {
            setOutputValue('解码失败，请检查输入格式');
        }
    };

    const encodeUrlFull = () => {
        try {
            setOutputValue(encodeURI(inputValue));
        } catch (e) {
            setOutputValue('编码失败');
        }
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-950 via-emerald-950 to-teal-950">
            {/* Fixed Header */}
            <header className="fixed top-0 left-0 right-0 z-50 bg-slate-950/90 backdrop-blur-md border-b border-emerald-500/20">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center h-16">
                        <div className="flex items-center">
                            <button
                                onClick={() => router.push('/')}
                                className="text-slate-400 hover:text-emerald-300 mr-4 transition-colors duration-200"
                            >
                                ← 返回
                            </button>
                            <h1 className="text-xl font-bold bg-gradient-to-r from-emerald-400 to-teal-400 bg-clip-text text-transparent">
                                URL编码解码工具
                            </h1>
                        </div>
                    </div>
                </div>
            </header>

            <div className="pt-20 p-8">
                <div className="max-w-6xl mx-auto">
                    {/* Tool Header */}
                    <div className="mb-8">
                        {/* <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-emerald-400 to-teal-500 text-white mb-4 shadow-lg shadow-emerald-500/25">
              <span className="text-2xl mr-2">🔗</span>
              <span className="font-semibold">URL编码解码</span>
              </div> */}
                        <p className="text-slate-400 text-lg">URL参数编码和解码转换工具</p>
                    </div>

                    {/* Tool Interface */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        {/* Input Section */}
                        <div className="space-y-4">
                            <h3 className="text-lg font-semibold text-slate-100">输入内容</h3>
                            <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-4">
                                <textarea
                                    value={inputValue}
                                    onChange={(e) => setInputValue(e.target.value)}
                                    placeholder="请输入需要编码或解码的URL或文本，例如：https://example.com/search?q=测试"
                                    className="w-full h-64 bg-transparent text-slate-100 placeholder-slate-400 resize-none focus:outline-none text-sm"
                                />
                            </div>
                            <div className="grid grid-cols-1 gap-3">
                                <button
                                    onClick={encodeUrl}
                                    className="py-3 px-6 rounded-lg bg-gradient-to-r from-emerald-400 to-teal-500 text-white font-semibold hover:shadow-lg hover:shadow-emerald-500/25 transition-all duration-200"
                                >
                                    URL组件编码 (encodeURIComponent)
                                </button>
                                <button
                                    onClick={encodeUrlFull}
                                    className="py-3 px-6 rounded-lg bg-gradient-to-r from-teal-500 to-cyan-600 text-white font-semibold hover:shadow-lg hover:shadow-teal-500/25 transition-all duration-200"
                                >
                                    完整URL编码 (encodeURI)
                                </button>
                                <button
                                    onClick={decodeUrl}
                                    className="py-3 px-6 rounded-lg bg-gradient-to-r from-slate-600 to-slate-700 text-white font-semibold hover:bg-gradient-to-r hover:from-slate-500 hover:to-slate-600 transition-all duration-200"
                                >
                                    URL解码
                                </button>
                            </div>
                        </div>

                        {/* Output Section */}
                        <div className="space-y-4">
                            <h3 className="text-lg font-semibold text-slate-100">输出结果</h3>
                            <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-4">
                                <textarea
                                    value={outputValue}
                                    readOnly
                                    placeholder="处理结果将显示在这里..."
                                    className="w-full h-64 bg-transparent text-slate-100 placeholder-slate-400 resize-none focus:outline-none text-sm"
                                />
                            </div>
                            <button
                                onClick={() => navigator.clipboard.writeText(outputValue)}
                                disabled={!outputValue}
                                className="w-full py-3 px-6 rounded-lg bg-slate-700 text-white font-semibold hover:bg-slate-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                复制结果
                            </button>
                        </div>
                    </div>

                    {/* Usage Examples */}
                    <div className="mt-12 bg-slate-900/40 backdrop-blur-md rounded-xl border border-slate-700/50 p-6">
                        <h4 className="text-lg font-semibold text-slate-100 mb-4">使用说明</h4>
                        <div className="space-y-3 text-sm text-slate-300">
                            <div>
                                <strong className="text-emerald-400">URL组件编码：</strong>
                                用于编码URL参数值，会编码所有特殊字符包括 / ? # 等
                            </div>
                            <div>
                                <strong className="text-teal-400">完整URL编码：</strong>
                                用于编码完整URL，保留URL结构字符如 : / ? # 等
                            </div>
                            <div>
                                <strong className="text-slate-400">示例：</strong>
                                &quot;测试 参数&quot; → &quot;测试%20参数&quot; (组件编码) 或
                                &quot;测试%20参数&quot; (完整编码)
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
