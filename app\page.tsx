'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { tools } from '@/app/toolsData';
import logoImg from '@/assets/images/logo2.png';
import Image from 'next/image';

export default function Page() {
    const [activeTab, setActiveTab] = useState('toolbox');
    const router = useRouter();

    const aiDocs = [
        { name: '公司文档', desc: '流程文档', count: '9999个文档' },
        { name: '对接文档', desc: 'API接口、第三方对接文档', count: '999个文档' },
        { name: '开发文档', desc: '技术规范、开发指南', count: '99个文档' },
        { name: '项目文档', desc: '项目需求、设计文档', count: '9个文档' },
    ];

    const sentryFeatures = [
        { name: '事件监控', desc: '实时监控系统事件和异常', icon: '👁️' },
        { name: '链路追踪', desc: '构建完整的事件处理链条', icon: '🔗' },
        { name: '自动化流程', desc: '自定义事件触发和响应规则', icon: '⚡' },
        { name: '数据分析', desc: '事件数据统计和分析报告', icon: '📊' },
    ];

    return (
        <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
            {/* Fixed Header */}
            <header className="fixed top-0 left-0 right-0 z-50 bg-gray-900/95 backdrop-blur-sm border-b border-gray-700">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center h-16">
                        <div className="flex items-center">
                            <Image
                                className="h-12 w-auto mr-3"
                                src={logoImg}
                                alt="logo"
                            />
                            <h1 className="text-2xl font-bold text-white">前端赋能平台</h1>
                        </div>
                        <div className="flex items-center space-x-4">
                            <span className="text-sm text-gray-400">v 0.0.1 Alpha</span>
                        </div>
                    </div>
                </div>
            </header>

            {/* Tab Navigation */}
            <nav className="fixed top-16 left-0 right-0 z-40 bg-gray-800/95 backdrop-blur-sm border-b border-gray-700">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex space-x-8">
                        <button
                            onClick={() => setActiveTab('toolbox')}
                            className={`relative py-4 px-1 font-medium text-sm transition-all duration-200 ${
                                activeTab === 'toolbox'
                                    ? 'text-cyan-400'
                                    : 'text-gray-400 hover:text-white'
                            }`}
                        >
                            {activeTab === 'toolbox' && (
                                <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full"></div>
                            )}
                            🧰 工具箱
                        </button>
                        <button
                            onClick={() => setActiveTab('ai-knowledge')}
                            className={`relative py-4 px-1 font-medium text-sm transition-all duration-200 ${
                                activeTab === 'ai-knowledge'
                                    ? 'text-green-400'
                                    : 'text-gray-400 hover:text-white'
                            }`}
                        >
                            {activeTab === 'ai-knowledge' && (
                                <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full"></div>
                            )}
                            📑 AI知识库
                        </button>
                        <button
                            onClick={() => setActiveTab('sentry')}
                            className={`relative py-4 px-1 font-medium text-sm transition-all duration-200 ${
                                activeTab === 'sentry'
                                    ? 'text-purple-400'
                                    : 'text-gray-400 hover:text-white'
                            }`}
                        >
                            {activeTab === 'sentry' && (
                                <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-purple-500 to-violet-500 rounded-full"></div>
                            )}
                            ☢️ 智能体
                        </button>
                    </div>
                </div>
            </nav>

            {/* Main Content */}
            <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pt-40">
                {/* Toolbox Tab */}
                {activeTab === 'toolbox' && (
                    <div>
                        <div className="mb-8">
                            <h2 className="text-3xl font-bold text-white mb-2">工具箱</h2>
                            <p className="text-gray-400">提供开发中常用的转换和处理工具</p>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            {tools.map((tool, index) => (
                                <div
                                    key={index}
                                    onClick={() => router.push(`/tool/${tool.key}`)}
                                    className="group relative bg-gray-800/50 backdrop-blur-sm rounded-lg border border-gray-700 p-6 hover:bg-gray-700/50 transition-all duration-300 cursor-pointer overflow-hidden"
                                >
                                    {/* Color bar at top */}
                                    <div
                                        className={`absolute top-0 left-0 right-0 h-1 bg-gradient-to-r ${tool.color} opacity-60 group-hover:opacity-100 transition-opacity`}
                                    ></div>

                                    {/* Glow effect */}
                                    <div
                                        className={`absolute inset-0 bg-gradient-to-r ${tool.color} opacity-0 group-hover:opacity-5 transition-opacity rounded-lg`}
                                    ></div>

                                    <div className="relative z-10">
                                        <div className="text-3xl mb-4 text-white">{tool.icon}</div>

                                        <p className="text-gray-400 text-sm group-hover:text-gray-300 transition-colors">
                                            {tool.desc}
                                        </p>
                                        <h3 className="text-lg font-semibold text-white mb-2 group-hover:text-cyan-300 transition-colors">
                                            {tool.name}
                                        </h3>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                )}

                {/* AI Knowledge Tab */}
                {activeTab === 'ai-knowledge' && (
                    <div>
                        <div className="mb-8">
                            <h2 className="text-3xl font-bold text-white mb-2">
                                AI知识库（开发中）
                            </h2>
                            <p className="text-gray-400">基于AI的智能文档检索和问答系统</p>
                        </div>

                        {/* Search Bar */}
                        <div className="mb-8">
                            <div className="max-w-2xl">
                                <div className="relative">
                                    <input
                                        type="text"
                                        placeholder="请输入您想了解的内容..."
                                        className="w-full px-4 py-3 pr-12 bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
                                    />

                                    <button className="absolute right-3 top-3 text-gray-400 hover:text-green-400 transition-colors">
                                        🔍
                                    </button>
                                </div>
                            </div>
                        </div>

                        {/* Document Categories */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {aiDocs.map((doc, index) => (
                                <div
                                    key={index}
                                    className="group bg-gray-800/50 backdrop-blur-sm rounded-lg border border-gray-700 p-6 hover:bg-gray-700/50 transition-all duration-300 cursor-pointer relative overflow-hidden"
                                >
                                    {/* Green color bar */}
                                    <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-green-500 to-emerald-500 opacity-60 group-hover:opacity-100 transition-opacity"></div>

                                    <div className="flex items-start justify-between relative z-10">
                                        <div>
                                            <h3 className="text-lg font-semibold text-white mb-2 group-hover:text-green-300 transition-colors">
                                                {doc.name}
                                            </h3>
                                            <p className="text-gray-400 text-sm mb-3 group-hover:text-gray-300 transition-colors">
                                                {doc.desc}
                                            </p>
                                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-900/50 text-green-300 border border-green-700">
                                                {doc.count}
                                            </span>
                                        </div>
                                        <div className="text-2xl">📚</div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                )}

                {/* Sentry Tab */}
                {activeTab === 'sentry' && (
                    <div>
                        <div className="mb-8">
                            <h2 className="text-3xl font-bold text-white mb-2">
                                智能体AGENT（开发中）
                            </h2>
                            <p className="text-gray-400">智能事件监控和自动化处理平台</p>
                        </div>

                        {/* Status Overview */}
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
                            <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg border border-gray-700 p-4 relative overflow-hidden">
                                <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-green-500 to-emerald-500"></div>
                                <div className="flex items-center">
                                    <div className="text-2xl mr-3">✅</div>
                                    <div>
                                        <p className="text-sm font-medium text-gray-400">
                                            系统状态
                                        </p>
                                        <p className="text-lg font-semibold text-green-400">
                                            正常运行
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg border border-gray-700 p-4 relative overflow-hidden">
                                <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 to-cyan-500"></div>
                                <div className="flex items-center">
                                    <div className="text-2xl mr-3">📊</div>
                                    <div>
                                        <p className="text-sm font-medium text-gray-400">
                                            活跃事件
                                        </p>
                                        <p className="text-lg font-semibold text-blue-400">24</p>
                                    </div>
                                </div>
                            </div>
                            <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg border border-gray-700 p-4 relative overflow-hidden">
                                <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-purple-500 to-violet-500"></div>
                                <div className="flex items-center">
                                    <div className="text-2xl mr-3">⚡</div>
                                    <div>
                                        <p className="text-sm font-medium text-gray-400">
                                            自动化规则
                                        </p>
                                        <p className="text-lg font-semibold text-purple-400">12</p>
                                    </div>
                                </div>
                            </div>
                            <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg border border-gray-700 p-4 relative overflow-hidden">
                                <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-orange-500 to-red-500"></div>
                                <div className="flex items-center">
                                    <div className="text-2xl mr-3">🔗</div>
                                    <div>
                                        <p className="text-sm font-medium text-gray-400">
                                            链路数量
                                        </p>
                                        <p className="text-lg font-semibold text-orange-400">8</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Features */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {sentryFeatures.map((feature, index) => (
                                <div
                                    key={index}
                                    className="group bg-gray-800/50 backdrop-blur-sm rounded-lg border border-gray-700 p-6 hover:bg-gray-700/50 transition-all duration-300 cursor-pointer relative overflow-hidden"
                                >
                                    <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-purple-500 to-violet-500 opacity-60 group-hover:opacity-100 transition-opacity"></div>
                                    <div className="flex items-start relative z-10">
                                        <div className="text-3xl mr-4">{feature.icon}</div>
                                        <div>
                                            <h3 className="text-lg font-semibold text-white mb-2 group-hover:text-purple-300 transition-colors">
                                                {feature.name}
                                            </h3>
                                            <p className="text-gray-400 text-sm group-hover:text-gray-300 transition-colors">
                                                {feature.desc}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                )}
            </main>

            {/* Footer */}
            <footer className="bg-gray-800/50 backdrop-blur-sm border-t border-gray-700 mt-16">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                    <div className="text-center text-gray-400 text-sm">
                        © 2025 X-LAB 大前端实验室赋能平台，提供工具箱、AI智能体等服务
                    </div>
                </div>
            </footer>
        </div>
    );
}
