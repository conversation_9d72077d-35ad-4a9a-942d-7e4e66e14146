'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';

interface Header {
    key: string;
    value: string;
    enabled: boolean;
}

export default function ApiDebugPage() {
    const router = useRouter();
    const [method, setMethod] = useState<'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'>('GET');
    const [url, setUrl] = useState('https://jsonplaceholder.typicode.com/posts/1');
    const [headers, setHeaders] = useState<Header[]>([
        { key: 'Content-Type', value: 'application/json', enabled: true },
    ]);
    const [body, setBody] = useState('');
    const [response, setResponse] = useState('');
    const [responseStatus, setResponseStatus] = useState<number | null>(null);
    const [responseTime, setResponseTime] = useState<number | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [activeTab, setActiveTab] = useState<'body' | 'headers'>('body');

    const addHeader = () => {
        setHeaders([...headers, { key: '', value: '', enabled: true }]);
    };

    const updateHeader = (
        index: number,
        field: 'key' | 'value' | 'enabled',
        value: string | boolean,
    ) => {
        const newHeaders = [...headers];
        newHeaders[index] = { ...newHeaders[index], [field]: value };
        setHeaders(newHeaders);
    };

    const removeHeader = (index: number) => {
        setHeaders(headers.filter((_, i) => i !== index));
    };

    const sendRequest = async () => {
        setIsLoading(true);
        setResponse('');
        setResponseStatus(null);
        setResponseTime(null);

        const startTime = Date.now();

        try {
            const enabledHeaders = headers
                .filter((h) => h.enabled && h.key && h.value)
                .reduce((acc, h) => ({ ...acc, [h.key]: h.value }), {});

            const requestOptions: RequestInit = {
                method,
                headers: enabledHeaders,
            };

            if (method !== 'GET' && body) {
                requestOptions.body = body;
            }

            const res = await fetch(url, requestOptions);
            const endTime = Date.now();

            setResponseStatus(res.status);
            setResponseTime(endTime - startTime);

            const contentType = res.headers.get('content-type');
            let responseText = await res.text();

            if (contentType?.includes('application/json')) {
                try {
                    const jsonData = JSON.parse(responseText);
                    responseText = JSON.stringify(jsonData, null, 2);
                } catch (e) {
                    // 如果不是有效的JSON，保持原始文本
                }
            }

            setResponse(responseText);
        } catch (error) {
            const endTime = Date.now();
            setResponseTime(endTime - startTime);
            setResponse(`请求失败: ${(error as Error).message}`);
            setResponseStatus(0);
        } finally {
            setIsLoading(false);
        }
    };

    const getStatusColor = (status: number | null) => {
        if (!status) return 'text-red-400';
        if (status >= 200 && status < 300) return 'text-green-400';
        if (status >= 300 && status < 400) return 'text-yellow-400';
        return 'text-red-400';
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-950 via-violet-950 to-purple-950">
            <header className="fixed top-0 left-0 right-0 z-50 bg-slate-950/90 backdrop-blur-md border-b border-violet-500/20">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center h-16">
                        <div className="flex items-center">
                            <button
                                onClick={() => router.push('/')}
                                className="text-slate-400 hover:text-violet-300 mr-4 transition-colors duration-200"
                            >
                                ← 返回
                            </button>
                            <h1 className="text-xl font-bold bg-gradient-to-r from-violet-400 to-purple-400 bg-clip-text text-transparent">
                                API调试工具
                            </h1>
                        </div>
                    </div>
                </div>
            </header>

            <div className="pt-20 p-8">
                <div className="max-w-7xl mx-auto">
                    <div className="mb-8">
                        {/* <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-violet-400 to-purple-500 text-white mb-4 shadow-lg shadow-violet-500/25">
              <span className="text-2xl mr-2">🚀</span>
              <span className="font-semibold">API调试工具</span>
              </div> */}
                        <p className="text-slate-400 text-lg">HTTP接口测试和调试工具</p>
                    </div>

                    <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
                        {/* 请求配置区域 */}
                        <div className="space-y-6">
                            <h3 className="text-xl font-semibold text-slate-100">请求配置</h3>

                            {/* URL和方法 */}
                            <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-4">
                                <div className="flex gap-3 mb-4">
                                    <select
                                        value={method}
                                        onChange={(e) => setMethod(e.target.value as any)}
                                        className="bg-slate-800 text-slate-100 border border-slate-600 rounded-lg px-4 py-2 focus:outline-none focus:border-violet-400"
                                    >
                                        <option value="GET">GET</option>
                                        <option value="POST">POST</option>
                                        <option value="PUT">PUT</option>
                                        <option value="DELETE">DELETE</option>
                                        <option value="PATCH">PATCH</option>
                                    </select>
                                    <input
                                        type="text"
                                        value={url}
                                        onChange={(e) => setUrl(e.target.value)}
                                        placeholder="请输入API地址..."
                                        className="flex-1 bg-slate-800 text-slate-100 border border-slate-600 rounded-lg px-4 py-2 focus:outline-none focus:border-violet-400"
                                    />
                                </div>
                                <button
                                    onClick={sendRequest}
                                    disabled={isLoading || !url}
                                    className="w-full py-3 px-6 rounded-lg bg-gradient-to-r from-violet-500 to-purple-600 text-white font-semibold hover:shadow-lg hover:shadow-violet-500/25 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    {isLoading ? '发送中...' : '发送请求'}
                                </button>
                            </div>

                            {/* Tab Navigation */}
                            <div className="flex space-x-1 bg-slate-900/60 backdrop-blur-md rounded-xl p-1">
                                <button
                                    onClick={() => setActiveTab('headers')}
                                    className={`flex-1 py-2 px-4 rounded-lg font-medium transition-all duration-200 ${
                                        activeTab === 'headers'
                                            ? 'bg-violet-600 text-white'
                                            : 'text-slate-400 hover:text-white'
                                    }`}
                                >
                                    请求头
                                </button>
                                <button
                                    onClick={() => setActiveTab('body')}
                                    className={`flex-1 py-2 px-4 rounded-lg font-medium transition-all duration-200 ${
                                        activeTab === 'body'
                                            ? 'bg-violet-600 text-white'
                                            : 'text-slate-400 hover:text-white'
                                    }`}
                                >
                                    请求体
                                </button>
                            </div>

                            {/* Headers Tab */}
                            {activeTab === 'headers' && (
                                <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-4">
                                    <div className="flex justify-between items-center mb-4">
                                        <h4 className="text-slate-300">请求头</h4>
                                        <button
                                            onClick={addHeader}
                                            className="px-3 py-1 bg-violet-600 text-white rounded-lg text-sm hover:bg-violet-500 transition-colors"
                                        >
                                            添加
                                        </button>
                                    </div>
                                    <div className="space-y-2 max-h-64 overflow-y-auto">
                                        {headers.map((header, index) => (
                                            <div key={index} className="flex gap-2 items-center">
                                                <input
                                                    type="checkbox"
                                                    checked={header.enabled}
                                                    onChange={(e) =>
                                                        updateHeader(
                                                            index,
                                                            'enabled',
                                                            e.target.checked,
                                                        )
                                                    }
                                                    className="rounded"
                                                />

                                                <input
                                                    type="text"
                                                    value={header.key}
                                                    onChange={(e) =>
                                                        updateHeader(index, 'key', e.target.value)
                                                    }
                                                    placeholder="键"
                                                    className="flex-1 bg-slate-800 text-slate-100 border border-slate-600 rounded px-2 py-1 text-sm focus:outline-none focus:border-violet-400"
                                                />

                                                <input
                                                    type="text"
                                                    value={header.value}
                                                    onChange={(e) =>
                                                        updateHeader(index, 'value', e.target.value)
                                                    }
                                                    placeholder="值"
                                                    className="flex-1 bg-slate-800 text-slate-100 border border-slate-600 rounded px-2 py-1 text-sm focus:outline-none focus:border-violet-400"
                                                />

                                                <button
                                                    onClick={() => removeHeader(index)}
                                                    className="px-2 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-500 transition-colors"
                                                >
                                                    删除
                                                </button>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}

                            {/* Body Tab */}
                            {activeTab === 'body' && (
                                <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-4">
                                    <h4 className="text-slate-300 mb-3">请求体</h4>
                                    <textarea
                                        value={body}
                                        onChange={(e) => setBody(e.target.value)}
                                        placeholder="请输入请求体内容（JSON格式）..."
                                        className="w-full h-64 bg-slate-800 text-slate-100 border border-slate-600 rounded-lg p-3 focus:outline-none focus:border-violet-400 font-mono text-sm"
                                    />
                                </div>
                            )}
                        </div>

                        {/* 响应区域 */}
                        <div className="space-y-6">
                            <h3 className="text-xl font-semibold text-slate-100">响应结果</h3>

                            {/* 响应状态 */}
                            {(responseStatus !== null || responseTime !== null) && (
                                <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-4">
                                    <div className="flex justify-between items-center">
                                        <div className="flex items-center gap-4">
                                            <span className="text-slate-400">状态码:</span>
                                            <span
                                                className={`font-semibold ${getStatusColor(responseStatus)}`}
                                            >
                                                {responseStatus || 'Error'}
                                            </span>
                                        </div>
                                        {responseTime !== null && (
                                            <div className="flex items-center gap-2">
                                                <span className="text-slate-400">响应时间:</span>
                                                <span className="text-slate-200">
                                                    {responseTime}ms
                                                </span>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            )}

                            {/* 响应内容 */}
                            <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-4">
                                <div className="flex justify-between items-center mb-3">
                                    <h4 className="text-slate-300">响应内容</h4>
                                    {response && (
                                        <button
                                            onClick={() => navigator.clipboard.writeText(response)}
                                            className="px-3 py-1 bg-slate-700 text-white rounded text-sm hover:bg-slate-600 transition-colors"
                                        >
                                            复制
                                        </button>
                                    )}
                                </div>
                                <div className="bg-slate-800 rounded-lg p-4 h-96 overflow-auto">
                                    <pre className="text-slate-100 text-sm whitespace-pre-wrap">
                                        {response || '响应内容将显示在这里...'}
                                    </pre>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* 使用说明 */}
                    <div className="mt-12 bg-slate-900/40 backdrop-blur-md rounded-xl border border-slate-700/50 p-6">
                        <h4 className="text-lg font-semibold text-slate-100 mb-4">使用说明</h4>
                        <div className="space-y-3 text-sm text-slate-300">
                            <div>
                                <strong className="text-violet-400">支持方法：</strong>
                                GET、POST、PUT、DELETE、PATCH等HTTP方法
                            </div>
                            <div>
                                <strong className="text-purple-400">请求头：</strong>
                                可以添加自定义请求头，支持认证、内容类型等
                            </div>
                            <div>
                                <strong className="text-violet-300">请求体：</strong>
                                支持JSON、XML、表单等格式的请求体
                            </div>
                            <div>
                                <strong className="text-purple-300">响应信息：</strong>
                                显示状态码、响应时间和完整的响应内容
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
