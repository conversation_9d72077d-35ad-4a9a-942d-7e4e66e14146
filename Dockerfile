#FROM sjb-storage-registry.cn-hangzhou.cr.aliyuncs.com/web/node:21.1.0
FROM sjb-storage-registry.cn-hangzhou.cr.aliyuncs.com/php/pnpm:21.1.0

# 拷贝dist包文件
COPY /.next/ /srv/www/frontend/.next/
COPY /package.json /srv/www/frontend/package.json
COPY /next.config.mjs /srv/www/frontend/next.config.mjs
COPY /public/ /srv/www/frontend/public/
#COPY /package-lock.json /srv/www/frontend/package-lock.json
COPY /node_modules/ /srv/www/frontend/node_modules/
# 拷贝nginx配置文件
#ADD nginx.conf /e-c/nginx/nginx.conf
#COPY frontend.conf /etc/nginx/conf.d/default.conf

WORKDIR /srv/www/frontend

#EXPOSE 80
EXPOSE 3000

# 启动 next 14 项目的服务器
CMD ["pnpm", "start"]

