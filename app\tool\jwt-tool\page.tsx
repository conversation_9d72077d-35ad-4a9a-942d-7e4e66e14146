'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import ToolSidebar from '../../../components/ToolSidebar';
import ToastContainer from '../../../components/ToastContainer';
import { useToast } from '../../../hooks/useToast';

export default function JwtToolPage() {
    const router = useRouter();
    const { toasts, showSuccess, showError, removeToast } = useToast();
    const [jwtToken, setJwtToken] = useState('');
    const [decodedHeader, setDecodedHeader] = useState('');
    const [decodedPayload, setDecodedPayload] = useState('');
    const [signature, setSignature] = useState('');
    const [secret, setSecret] = useState('your-256-bit-secret');
    const [headerInput, setHeaderInput] = useState('{"alg": "HS256", "typ": "JWT"}');
    const [payloadInput, setPayloadInput] = useState(
        '{"sub": "1234567890", "name": "<PERSON>", "iat": 1516239022}',
    );
    const [generatedToken, setGeneratedToken] = useState('');
    const [error, setError] = useState('');

    // Base64 URL编码/解码
    const base64UrlEncode = (str: string): string => {
        return btoa(str).replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
    };

    const base64UrlDecode = (str: string): string => {
        str = str.replace(/-/g, '+').replace(/_/g, '/');
        while (str.length % 4) {
            str += '=';
        }
        return atob(str);
    };

    // 简单的HMAC SHA256实现（实际项目中建议使用crypto-js）
    const hmacSha256 = (message: string, secret: string): string => {
        // 这里使用简化版本，实际项目中应使用真正的HMAC SHA256
        let hash = 0;
        const combined = message + secret;
        for (let i = 0; i < combined.length; i++) {
            const char = combined.charCodeAt(i);
            hash = (hash << 5) - hash + char;
            hash = hash & hash;
        }
        return Math.abs(hash).toString(16).padStart(32, '0');
    };

    const decodeJWT = () => {
        try {
            setError('');
            const parts = jwtToken.split('.');
            if (parts.length !== 3) {
                throw new Error('JWT格式错误，应包含3个部分');
            }

            const header = JSON.parse(base64UrlDecode(parts[0]));
            const payload = JSON.parse(base64UrlDecode(parts[1]));

            setDecodedHeader(JSON.stringify(header, null, 2));
            setDecodedPayload(JSON.stringify(payload, null, 2));
            setSignature(parts[2]);
        } catch (e) {
            setError('JWT解码失败: ' + (e as Error).message);
            setDecodedHeader('');
            setDecodedPayload('');
            setSignature('');
        }
    };

    const generateJWT = () => {
        try {
            setError('');
            const header = JSON.parse(headerInput);
            const payload = JSON.parse(payloadInput);

            const encodedHeader = base64UrlEncode(JSON.stringify(header));
            const encodedPayload = base64UrlEncode(JSON.stringify(payload));
            const data = `${encodedHeader}.${encodedPayload}`;

            const signature = base64UrlEncode(hmacSha256(data, secret));
            const token = `${data}.${signature}`;

            setGeneratedToken(token);
        } catch (e) {
            setError('JWT生成失败: ' + (e as Error).message);
            setGeneratedToken('');
        }
    };

    const verifyJWT = () => {
        try {
            setError('');
            const parts = jwtToken.split('.');
            if (parts.length !== 3) {
                throw new Error('JWT格式错误');
            }

            const data = `${parts[0]}.${parts[1]}`;
            const expectedSignature = base64UrlEncode(hmacSha256(data, secret));

            if (parts[2] === expectedSignature) {
                setError('✅ JWT签名验证成功');
            } else {
                setError('❌ JWT签名验证失败');
            }
        } catch (e) {
            setError('JWT验证失败: ' + (e as Error).message);
        }
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-950 via-amber-950 to-yellow-950">
            <header className="fixed top-0 left-0 right-0 z-50 bg-slate-950/90 backdrop-blur-md border-b border-amber-500/20">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pl-80">
                    <div className="flex justify-between items-center h-16">
                        <div className="flex items-center">
                            <h1 className="text-xl font-bold bg-gradient-to-r from-amber-400 to-yellow-400 bg-clip-text text-transparent">
                                JWT工具
                            </h1>
                        </div>
                    </div>
                </div>
            </header>

            {/* Sidebar */}
            <ToolSidebar currentTool="jwt-tool" />

            {/* Toast Container */}
            <ToastContainer toasts={toasts} removeToast={removeToast} />

            <div className="pt-20 p-8 pl-80">
                <div className="max-w-7xl mx-auto">
                    <div className="mb-8">
                        {/* <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-amber-400 to-yellow-500 text-white mb-4 shadow-lg shadow-amber-500/25">
              <span className="text-2xl mr-2">🔑</span>
              <span className="font-semibold">JWT加密解密和校验</span>
              </div> */}
                        <p className="text-slate-400 text-lg">
                            JSON Web Token 编码、解码和验证工具
                        </p>
                    </div>

                    <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
                        {/* JWT解码部分 */}
                        <div className="space-y-6">
                            <h3 className="text-xl font-semibold text-slate-100">JWT解码</h3>

                            <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-4">
                                <label className="block text-slate-300 mb-2">JWT Token</label>
                                <textarea
                                    value={jwtToken}
                                    onChange={(e) => setJwtToken(e.target.value)}
                                    placeholder="请输入JWT Token..."
                                    className="w-full h-32 bg-slate-800 text-slate-100 border border-slate-600 rounded-lg p-3 focus:outline-none focus:border-amber-400 font-mono text-sm"
                                />
                            </div>

                            <div className="flex gap-3">
                                <button
                                    onClick={decodeJWT}
                                    className="flex-1 py-3 px-6 rounded-lg bg-gradient-to-r from-amber-400 to-yellow-500 text-white font-semibold hover:shadow-lg hover:shadow-amber-500/25 transition-all duration-200"
                                >
                                    解码JWT
                                </button>
                                <button
                                    onClick={verifyJWT}
                                    className="flex-1 py-3 px-6 rounded-lg bg-gradient-to-r from-emerald-500 to-green-600 text-white font-semibold hover:shadow-lg hover:shadow-emerald-500/25 transition-all duration-200"
                                >
                                    验证签名
                                </button>
                            </div>

                            <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-4">
                                <label className="block text-slate-300 mb-2">密钥 (用于验证)</label>
                                <input
                                    type="text"
                                    value={secret}
                                    onChange={(e) => setSecret(e.target.value)}
                                    className="w-full bg-slate-800 text-slate-100 border border-slate-600 rounded-lg p-3 focus:outline-none focus:border-amber-400"
                                />
                            </div>

                            {/* 解码结果 */}
                            <div className="space-y-4">
                                <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-4">
                                    <label className="block text-slate-300 mb-2">Header</label>
                                    <pre className="bg-slate-800 text-slate-100 p-3 rounded-lg text-sm overflow-auto">
                                        {decodedHeader || '解码后的Header将显示在这里...'}
                                    </pre>
                                </div>

                                <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-4">
                                    <label className="block text-slate-300 mb-2">Payload</label>
                                    <pre className="bg-slate-800 text-slate-100 p-3 rounded-lg text-sm overflow-auto">
                                        {decodedPayload || '解码后的Payload将显示在这里...'}
                                    </pre>
                                </div>

                                <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-4">
                                    <label className="block text-slate-300 mb-2">Signature</label>
                                    <div className="bg-slate-800 text-slate-100 p-3 rounded-lg text-sm font-mono break-all">
                                        {signature || '签名将显示在这里...'}
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* JWT生成部分 */}
                        <div className="space-y-6">
                            <h3 className="text-xl font-semibold text-slate-100">JWT生成</h3>

                            <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-4">
                                <label className="block text-slate-300 mb-2">Header</label>
                                <textarea
                                    value={headerInput}
                                    onChange={(e) => setHeaderInput(e.target.value)}
                                    className="w-full h-24 bg-slate-800 text-slate-100 border border-slate-600 rounded-lg p-3 focus:outline-none focus:border-amber-400 font-mono text-sm"
                                />
                            </div>

                            <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-4">
                                <label className="block text-slate-300 mb-2">Payload</label>
                                <textarea
                                    value={payloadInput}
                                    onChange={(e) => setPayloadInput(e.target.value)}
                                    className="w-full h-32 bg-slate-800 text-slate-100 border border-slate-600 rounded-lg p-3 focus:outline-none focus:border-amber-400 font-mono text-sm"
                                />
                            </div>

                            <button
                                onClick={generateJWT}
                                className="w-full py-3 px-6 rounded-lg bg-gradient-to-r from-yellow-500 to-amber-600 text-white font-semibold hover:shadow-lg hover:shadow-yellow-500/25 transition-all duration-200"
                            >
                                生成JWT
                            </button>

                            <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-4">
                                <label className="block text-slate-300 mb-2">生成的JWT Token</label>
                                <textarea
                                    value={generatedToken}
                                    readOnly
                                    className="w-full h-32 bg-slate-800 text-slate-100 border border-slate-600 rounded-lg p-3 focus:outline-none font-mono text-sm"
                                />

                                <button
                                    onClick={async () => {
                                        if (generatedToken) {
                                            try {
                                                await navigator.clipboard.writeText(generatedToken);
                                                showSuccess('Token复制成功！');
                                            } catch (err) {
                                                showError('复制失败，请手动复制');
                                            }
                                        }
                                    }}
                                    disabled={!generatedToken}
                                    className="mt-3 w-full py-2 px-4 rounded-lg bg-slate-700 text-white font-semibold hover:bg-slate-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    复制Token
                                </button>
                            </div>
                        </div>
                    </div>

                    {error && (
                        <div className="mt-6 p-4 bg-red-900/50 border border-red-500/50 rounded-lg text-red-300 text-sm">
                            {error}
                        </div>
                    )}

                    {/* 使用说明 */}
                    <div className="mt-12 bg-slate-900/40 backdrop-blur-md rounded-xl border border-slate-700/50 p-6">
                        <h4 className="text-lg font-semibold text-slate-100 mb-4">JWT说明</h4>
                        <div className="space-y-3 text-sm text-slate-300">
                            <div>
                                <strong className="text-amber-400">JWT结构：</strong>
                                由Header、Payload、Signature三部分组成，用.分隔
                            </div>
                            <div>
                                <strong className="text-yellow-400">Header：</strong>
                                包含算法类型和令牌类型信息
                            </div>
                            <div>
                                <strong className="text-amber-300">Payload：</strong>
                                包含声明信息，如用户ID、过期时间等
                            </div>
                            <div>
                                <strong className="text-yellow-300">Signature：</strong>
                                用于验证令牌的完整性和真实性
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
