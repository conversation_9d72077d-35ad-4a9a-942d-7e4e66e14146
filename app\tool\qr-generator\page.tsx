'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';

export default function QrGeneratorPage() {
    const router = useRouter();
    const [inputText, setInputText] = useState('');
    const [qrSize, setQrSize] = useState(200);
    const [errorLevel, setErrorLevel] = useState<'L' | 'M' | 'Q' | 'H'>('M');
    const [qrDataUrl, setQrDataUrl] = useState('');

    // 简单的二维码生成（实际项目中建议使用qrcode库）
    const generateQR = async () => {
        if (!inputText.trim()) {
            setQrDataUrl('');
            return;
        }

        // 使用在线API生成二维码（示例）
        // 实际项目中建议使用本地库如qrcode.js
        const apiUrl = `https://api.qrserver.com/v1/create-qr-code/?size=${qrSize}x${qrSize}&data=${encodeURIComponent(inputText)}&ecc=${errorLevel}`;
        setQrDataUrl(apiUrl);
    };

    const downloadQR = () => {
        if (!qrDataUrl) return;

        const link = document.createElement('a');
        link.href = qrDataUrl;
        link.download = 'qrcode.png';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const presetTexts = [
        { label: '网站链接', value: 'https://example.com' },
        { label: '微信号', value: 'weixin://contacts/profile/your_wechat_id' },
        { label: 'WiFi连接', value: 'WIFI:T:WPA;S:WiFi名称;P:密码;H:false;;' },
        { label: '电话号码', value: 'tel:+86138000000000' },
        { label: '邮箱地址', value: 'mailto:<EMAIL>' },
        { label: '短信', value: 'sms:138000000000?body=Hello' },
    ];

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-950 via-teal-950 to-cyan-950">
            {/* Fixed Header */}
            <header className="fixed top-0 left-0 right-0 z-50 bg-slate-950/90 backdrop-blur-md border-b border-teal-500/20">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center h-16">
                        <div className="flex items-center">
                            <button
                                onClick={() => router.push('/')}
                                className="text-slate-400 hover:text-teal-300 mr-4 transition-colors duration-200"
                            >
                                ← 返回
                            </button>
                            <h1 className="text-xl font-bold bg-gradient-to-r from-teal-400 to-cyan-400 bg-clip-text text-transparent">
                                二维码生成工具
                            </h1>
                        </div>
                    </div>
                </div>
            </header>

            <div className="pt-20 p-8">
                <div className="max-w-6xl mx-auto">
                    {/* Tool Header */}
                    <div className="mb-8">
                        <p className="text-slate-400 text-lg">将文本、链接等内容转换为二维码</p>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        {/* Input Section */}
                        <div className="space-y-6">
                            <h3 className="text-lg font-semibold text-slate-100">输入内容</h3>

                            {/* Text Input */}
                            <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-4">
                                <textarea
                                    value={inputText}
                                    onChange={(e) => setInputText(e.target.value)}
                                    placeholder="请输入要生成二维码的内容（文本、链接、WiFi信息等）"
                                    className="w-full h-32 bg-transparent text-slate-100 placeholder-slate-400 resize-none focus:outline-none text-sm"
                                />
                            </div>

                            {/* Preset Templates */}
                            <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-4">
                                <label className="block text-slate-300 mb-3">快速模板</label>
                                <div className="grid grid-cols-2 gap-2">
                                    {presetTexts.map((preset, index) => (
                                        <button
                                            key={index}
                                            onClick={() => setInputText(preset.value)}
                                            className="p-2 text-xs bg-slate-800/50 hover:bg-slate-700/50 text-slate-300 rounded-lg transition-colors text-left"
                                        >
                                            {preset.label}
                                        </button>
                                    ))}
                                </div>
                            </div>

                            {/* Settings */}
                            <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-4 space-y-4">
                                {/* Size Setting */}
                                <div>
                                    <label className="block text-slate-300 mb-2">
                                        尺寸大小: {qrSize}px
                                    </label>
                                    <input
                                        type="range"
                                        min="100"
                                        max="500"
                                        step="50"
                                        value={qrSize}
                                        onChange={(e) => setQrSize(parseInt(e.target.value))}
                                        className="w-full h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer"
                                    />
                                </div>

                                {/* Error Correction Level */}
                                <div>
                                    <label className="block text-slate-300 mb-2">容错级别</label>
                                    <select
                                        value={errorLevel}
                                        onChange={(e) => setErrorLevel(e.target.value as any)}
                                        className="w-full bg-slate-800 text-slate-100 border border-slate-600 rounded-lg p-2 focus:outline-none focus:border-teal-400"
                                    >
                                        <option value="L">L - 低 (~7%)</option>
                                        <option value="M">M - 中 (~15%)</option>
                                        <option value="Q">Q - 高 (~25%)</option>
                                        <option value="H">H - 最高 (~30%)</option>
                                    </select>
                                </div>
                            </div>

                            <button
                                onClick={generateQR}
                                className="w-full py-3 px-6 rounded-lg bg-gradient-to-r from-teal-400 to-cyan-500 text-white font-semibold hover:shadow-lg hover:shadow-teal-500/25 transition-all duration-200"
                            >
                                生成二维码
                            </button>
                        </div>

                        {/* Output Section */}
                        <div className="space-y-4">
                            <h3 className="text-lg font-semibold text-slate-100">二维码预览</h3>

                            <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-8 flex flex-col items-center justify-center min-h-[400px]">
                                {qrDataUrl ? (
                                    <div className="text-center">
                                        <img
                                            src={qrDataUrl}
                                            alt="Generated QR Code"
                                            className="mx-auto mb-4 rounded-lg shadow-lg"
                                            style={{ width: qrSize, height: qrSize }}
                                        />

                                        <p className="text-slate-400 text-sm mb-4">
                                            {qrSize}x{qrSize}px, 容错级别: {errorLevel}
                                        </p>
                                    </div>
                                ) : (
                                    <div className="text-center text-slate-400">
                                        <div className="text-6xl mb-4">📱</div>
                                        <p>输入内容后点击生成按钮</p>
                                    </div>
                                )}
                            </div>

                            {qrDataUrl && (
                                <div className="flex gap-3">
                                    <button
                                        onClick={downloadQR}
                                        className="flex-1 py-3 px-6 rounded-lg bg-gradient-to-r from-cyan-500 to-teal-600 text-white font-semibold hover:shadow-lg hover:shadow-cyan-500/25 transition-all duration-200"
                                    >
                                        下载二维码
                                    </button>
                                    <button
                                        onClick={() => navigator.clipboard.writeText(qrDataUrl)}
                                        className="px-6 py-3 rounded-lg bg-slate-700 text-white hover:bg-slate-600 transition-colors"
                                    >
                                        复制链接
                                    </button>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Usage Examples */}
                    <div className="mt-12 bg-slate-900/40 backdrop-blur-md rounded-xl border border-slate-700/50 p-6">
                        <h4 className="text-lg font-semibold text-slate-100 mb-4">使用说明</h4>
                        <div className="space-y-3 text-sm text-slate-300">
                            <div>
                                <strong className="text-teal-400">支持内容：</strong>
                                文本、网址、WiFi信息、联系人信息、电话号码、邮箱等
                            </div>
                            <div>
                                <strong className="text-cyan-400">容错级别：</strong>
                                级别越高，二维码越复杂，但损坏后仍可识别的能力越强
                            </div>
                            <div>
                                <strong className="text-slate-400">WiFi格式：</strong>
                                WIFI:T:WPA;S:网络名称;P:密码;H:false;;
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
