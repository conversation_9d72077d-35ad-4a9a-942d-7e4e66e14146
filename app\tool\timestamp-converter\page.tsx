'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function TimestampConverterPage() {
    const router = useRouter();
    const [currentTime, setCurrentTime] = useState(new Date());
    const [timestampInput, setTimestampInput] = useState('');
    const [dateInput, setDateInput] = useState('');
    const [timestampResult, setTimestampResult] = useState('');
    const [dateResult, setDateResult] = useState('');
    const [timestampUnit, setTimestampUnit] = useState<'seconds' | 'milliseconds'>('seconds');

    useEffect(() => {
        const timer = setInterval(() => {
            setCurrentTime(new Date());
        }, 1000);
        return () => clearInterval(timer);
    }, []);

    const timestampToDate = () => {
        try {
            const timestamp = parseInt(timestampInput);
            if (isNaN(timestamp)) {
                setDateResult('请输入有效的时间戳');
                return;
            }

            const date =
                timestampUnit === 'seconds' ? new Date(timestamp * 1000) : new Date(timestamp);

            if (isNaN(date.getTime())) {
                setDateResult('无效的时间戳');
                return;
            }

            const result = [
                `标准格式: ${date.toLocaleString('zh-CN')}`,
                `ISO格式: ${date.toISOString()}`,
                `UTC格式: ${date.toUTCString()}`,
                `年月日: ${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`,
                `时分秒: ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`,
            ].join('\n');

            setDateResult(result);
        } catch (error) {
            setDateResult('转换失败: ' + (error as Error).message);
        }
    };

    const dateToTimestamp = () => {
        try {
            const date = new Date(dateInput);
            if (isNaN(date.getTime())) {
                setTimestampResult('请输入有效的日期格式');
                return;
            }

            const timestamp = date.getTime();
            const result = [
                `秒级时间戳: ${Math.floor(timestamp / 1000)}`,
                `毫秒级时间戳: ${timestamp}`,
                `相对时间: ${getRelativeTime(date)}`,
                `星期: ${['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'][date.getDay()]}`,
                `第${Math.ceil(date.getDate() / 7)}周`,
            ].join('\n');

            setTimestampResult(result);
        } catch (error) {
            setTimestampResult('转换失败: ' + (error as Error).message);
        }
    };

    const getRelativeTime = (date: Date): string => {
        const now = new Date();
        const diff = now.getTime() - date.getTime();
        const seconds = Math.floor(diff / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);

        if (days > 0) return `${days}天前`;
        if (hours > 0) return `${hours}小时前`;
        if (minutes > 0) return `${minutes}分钟前`;
        if (seconds > 0) return `${seconds}秒前`;
        return '刚刚';
    };

    const getCurrentTimestamp = () => {
        const now = Date.now();
        setTimestampInput(Math.floor(now / 1000).toString());
        timestampToDate();
    };

    const getCurrentDate = () => {
        setDateInput(new Date().toISOString().slice(0, 16));
        dateToTimestamp();
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-950 via-blue-950 to-indigo-950">
            {/* Fixed Header */}
            <header className="fixed top-0 left-0 right-0 z-50 bg-slate-950/90 backdrop-blur-md border-b border-indigo-500/20">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center h-16">
                        <div className="flex items-center">
                            <button
                                onClick={() => router.push('/')}
                                className="text-slate-400 hover:text-indigo-300 mr-4 transition-colors duration-300"
                            >
                                ← 返回
                            </button>
                            <h1 className="text-xl font-bold bg-gradient-to-r from-indigo-400 to-blue-400 bg-clip-text text-transparent">
                                时间戳转换工具
                            </h1>
                        </div>
                    </div>
                </div>
            </header>

            <div className="pt-20 p-8">
                <div className="max-w-6xl mx-auto">
                    {/* Tool Header */}
                    <div className="mb-8">
                        <p className="text-slate-400 text-lg">Unix时间戳与可读日期格式之间的转换</p>
                    </div>

                    {/* Current Time Display */}
                    <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-6 mb-8">
                        <h3 className="text-lg font-semibold text-slate-100 mb-4">当前时间</h3>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div className="bg-slate-800/50 rounded-lg p-4">
                                <div className="text-slate-400 mb-1">当前日期时间</div>
                                <div className="text-indigo-300 font-mono">
                                    {currentTime.toLocaleString('zh-CN')}
                                </div>
                            </div>
                            <div className="bg-slate-800/50 rounded-lg p-4">
                                <div className="text-slate-400 mb-1">秒级时间戳</div>
                                <div className="text-blue-300 font-mono">
                                    {Math.floor(currentTime.getTime() / 1000)}
                                </div>
                            </div>
                            <div className="bg-slate-800/50 rounded-lg p-4">
                                <div className="text-slate-400 mb-1">毫秒级时间戳</div>
                                <div className="text-cyan-300 font-mono">
                                    {currentTime.getTime()}
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Conversion Tools */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        {/* Timestamp to Date */}
                        <div className="space-y-4">
                            <h3 className="text-lg font-semibold text-slate-100">时间戳转日期</h3>
                            <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-4">
                                <div className="mb-4">
                                    <div className="flex gap-2 mb-2">
                                        <button
                                            onClick={() => setTimestampUnit('seconds')}
                                            className={`px-3 py-1 rounded text-sm transition-all duration-300 ${
                                                timestampUnit === 'seconds'
                                                    ? 'bg-indigo-500 text-white'
                                                    : 'bg-slate-700 text-slate-300 hover:bg-slate-600'
                                            }`}
                                        >
                                            秒
                                        </button>
                                        <button
                                            onClick={() => setTimestampUnit('milliseconds')}
                                            className={`px-3 py-1 rounded text-sm transition-all duration-300 ${
                                                timestampUnit === 'milliseconds'
                                                    ? 'bg-indigo-500 text-white'
                                                    : 'bg-slate-700 text-slate-300 hover:bg-slate-600'
                                            }`}
                                        >
                                            毫秒
                                        </button>
                                    </div>
                                    <input
                                        type="text"
                                        value={timestampInput}
                                        onChange={(e) => setTimestampInput(e.target.value)}
                                        placeholder={`请输入${timestampUnit === 'seconds' ? '秒级' : '毫秒级'}时间戳`}
                                        className="w-full px-4 py-3 bg-slate-800 border border-slate-600 rounded-lg text-slate-100 placeholder-slate-500 focus:ring-2 focus:ring-indigo-500 focus:border-transparent font-mono"
                                    />
                                </div>
                                <div className="flex gap-2 mb-4">
                                    <button
                                        onClick={timestampToDate}
                                        className="flex-1 py-2 px-4 rounded-lg bg-gradient-to-r from-indigo-400 to-blue-500 text-white font-semibold hover:shadow-lg hover:shadow-indigo-500/25 transition-all duration-300"
                                    >
                                        转换
                                    </button>
                                    <button
                                        onClick={getCurrentTimestamp}
                                        className="px-4 py-2 rounded-lg bg-slate-700 text-white hover:bg-slate-600 transition-colors duration-300"
                                    >
                                        当前时间戳
                                    </button>
                                </div>
                                <textarea
                                    value={dateResult}
                                    readOnly
                                    placeholder="转换结果将显示在这里..."
                                    className="w-full h-32 bg-slate-800 border border-slate-600 rounded-lg text-slate-100 placeholder-slate-500 resize-none focus:outline-none p-3 text-sm font-mono"
                                />
                            </div>
                        </div>

                        {/* Date to Timestamp */}
                        <div className="space-y-4">
                            <h3 className="text-lg font-semibold text-slate-100">日期转时间戳</h3>
                            <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-4">
                                <div className="mb-4">
                                    <input
                                        type="datetime-local"
                                        value={dateInput}
                                        onChange={(e) => setDateInput(e.target.value)}
                                        className="w-full px-4 py-3 bg-slate-800 border border-slate-600 rounded-lg text-slate-100 focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                                    />
                                </div>
                                <div className="flex gap-2 mb-4">
                                    <button
                                        onClick={dateToTimestamp}
                                        className="flex-1 py-2 px-4 rounded-lg bg-gradient-to-r from-indigo-400 to-blue-500 text-white font-semibold hover:shadow-lg hover:shadow-indigo-500/25 transition-all duration-300"
                                    >
                                        转换
                                    </button>
                                    <button
                                        onClick={getCurrentDate}
                                        className="px-4 py-2 rounded-lg bg-slate-700 text-white hover:bg-slate-600 transition-colors duration-300"
                                    >
                                        当前时间
                                    </button>
                                </div>
                                <textarea
                                    value={timestampResult}
                                    readOnly
                                    placeholder="转换结果将显示在这里..."
                                    className="w-full h-32 bg-slate-800 border border-slate-600 rounded-lg text-slate-100 placeholder-slate-500 resize-none focus:outline-none p-3 text-sm font-mono"
                                />
                            </div>
                        </div>
                    </div>

                    {/* Usage Examples */}
                    <div className="mt-12 bg-slate-900/40 backdrop-blur-md rounded-xl border border-slate-700/50 p-6">
                        <h4 className="text-lg font-semibold text-slate-100 mb-4">
                            常用日期格式示例
                        </h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-slate-300">
                            <div>
                                <h5 className="font-medium text-indigo-300 mb-2">支持的输入格式</h5>
                                <ul className="space-y-1 text-slate-400">
                                    <li>• 2024-01-01 12:00:00</li>
                                    <li>• 2024/01/01 12:00:00</li>
                                    <li>• Jan 1, 2024 12:00:00</li>
                                    <li>• 2024-01-01T12:00:00</li>
                                </ul>
                            </div>
                            <div>
                                <h5 className="font-medium text-blue-300 mb-2">时间戳说明</h5>
                                <ul className="space-y-1 text-slate-400">
                                    <li>• 秒级：10位数字 (1640995200)</li>
                                    <li>• 毫秒级：13位数字 (1640995200000)</li>
                                    <li>• Unix时间戳从1970年1月1日开始计算</li>
                                    <li>• 时区会影响显示结果</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
