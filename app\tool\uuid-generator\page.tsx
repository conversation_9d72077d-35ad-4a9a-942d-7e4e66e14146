'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';

export default function UuidGeneratorPage() {
    const router = useRouter();
    const [uuids, setUuids] = useState<string[]>([]);
    const [count, setCount] = useState(1);
    const [format, setFormat] = useState<'standard' | 'uppercase' | 'nohyphens'>('standard');

    const generateUUIDs = () => {
        const newUuids: string[] = [];
        for (let i = 0; i < count; i++) {
            let uuid = crypto.randomUUID();

            switch (format) {
                case 'uppercase':
                    uuid = uuid.toUpperCase();
                    break;
                case 'nohyphens':
                    uuid = uuid.replace(/-/g, '');
                    break;
                default:
                    // standard format, no change needed
                    break;
            }

            newUuids.push(uuid);
        }
        setUuids(newUuids);
    };

    const copyAllUUIDs = () => {
        const allUuids = uuids.join('\n');
        navigator.clipboard.writeText(allUuids);
    };

    const copyUUID = (uuid: string) => {
        navigator.clipboard.writeText(uuid);
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-950 via-amber-950 to-orange-950">
            {/* Fixed Header */}
            <header className="fixed top-0 left-0 right-0 z-50 bg-slate-950/90 backdrop-blur-md border-b border-amber-500/20">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center h-16">
                        <div className="flex items-center">
                            <button
                                onClick={() => router.push('/')}
                                className="text-slate-400 hover:text-amber-300 mr-4 transition-colors duration-200"
                            >
                                ← 返回
                            </button>
                            <h1 className="text-xl font-bold bg-gradient-to-r from-amber-400 to-orange-400 bg-clip-text text-transparent">
                                UUID生成器
                            </h1>
                        </div>
                    </div>
                </div>
            </header>

            <div className="pt-20 p-8">
                <div className="max-w-6xl mx-auto">
                    {/* Tool Header */}
                    <div className="mb-8">
                        {/* <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-amber-400 to-orange-500 text-white mb-4 shadow-lg shadow-amber-500/25">
              <span className="text-2xl mr-2">🆔</span>
              <span className="font-semibold">UUID生成器</span>
              </div> */}
                        <p className="text-slate-400 text-lg">
                            生成全球唯一标识符（UUID），支持多种格式
                        </p>
                    </div>

                    {/* Tool Interface */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        {/* Control Section */}
                        <div className="space-y-6">
                            <h3 className="text-lg font-semibold text-slate-100">生成设置</h3>

                            {/* Count Setting */}
                            <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-6">
                                <label className="block text-slate-300 mb-3">生成数量</label>
                                <div className="flex items-center space-x-4">
                                    <input
                                        type="range"
                                        min="1"
                                        max="100"
                                        value={count}
                                        onChange={(e) => setCount(parseInt(e.target.value))}
                                        className="flex-1 h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer slider"
                                    />

                                    <span className="text-amber-400 font-semibold w-12 text-center">
                                        {count}
                                    </span>
                                </div>
                            </div>

                            {/* Format Setting */}
                            <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-6">
                                <label className="block text-slate-300 mb-3">输出格式</label>
                                <div className="space-y-2">
                                    <label className="flex items-center text-slate-300">
                                        <input
                                            type="radio"
                                            name="format"
                                            value="standard"
                                            checked={format === 'standard'}
                                            onChange={(e) => setFormat(e.target.value as any)}
                                            className="mr-3 text-amber-500 focus:ring-amber-500"
                                        />
                                        标准格式 (xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx)
                                    </label>
                                    <label className="flex items-center text-slate-300">
                                        <input
                                            type="radio"
                                            name="format"
                                            value="uppercase"
                                            checked={format === 'uppercase'}
                                            onChange={(e) => setFormat(e.target.value as any)}
                                            className="mr-3 text-amber-500 focus:ring-amber-500"
                                        />
                                        大写格式 (XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX)
                                    </label>
                                    <label className="flex items-center text-slate-300">
                                        <input
                                            type="radio"
                                            name="format"
                                            value="nohyphens"
                                            checked={format === 'nohyphens'}
                                            onChange={(e) => setFormat(e.target.value as any)}
                                            className="mr-3 text-amber-500 focus:ring-amber-500"
                                        />
                                        无连字符 (xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx)
                                    </label>
                                </div>
                            </div>

                            <button
                                onClick={generateUUIDs}
                                className="w-full py-4 px-6 rounded-lg bg-gradient-to-r from-amber-400 to-orange-500 text-white font-semibold hover:shadow-lg hover:shadow-amber-500/25 transition-all duration-200"
                            >
                                生成UUID
                            </button>
                        </div>

                        {/* Output Section */}
                        <div className="space-y-4">
                            <div className="flex justify-between items-center">
                                <h3 className="text-lg font-semibold text-slate-100">生成结果</h3>
                                {uuids.length > 0 && (
                                    <button
                                        onClick={copyAllUUIDs}
                                        className="px-4 py-2 rounded-lg bg-slate-700 text-white text-sm hover:bg-slate-600 transition-colors"
                                    >
                                        复制全部
                                    </button>
                                )}
                            </div>

                            <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-4 max-h-96 overflow-y-auto">
                                {uuids.length === 0 ? (
                                    <div className="text-slate-400 text-center py-8">
                                        点击生成按钮创建UUID
                                    </div>
                                ) : (
                                    <div className="space-y-2">
                                        {uuids.map((uuid, index) => (
                                            <div
                                                key={index}
                                                className="flex items-center justify-between p-3 bg-slate-800/50 rounded-lg hover:bg-slate-700/50 transition-colors group"
                                            >
                                                <code className="text-slate-100 font-mono text-sm flex-1 mr-4">
                                                    {uuid}
                                                </code>
                                                <button
                                                    onClick={() => copyUUID(uuid)}
                                                    className="opacity-0 group-hover:opacity-100 px-3 py-1 rounded bg-amber-600 text-white text-xs hover:bg-amber-500 transition-all"
                                                >
                                                    复制
                                                </button>
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Usage Examples */}
                    <div className="mt-12 bg-slate-900/40 backdrop-blur-md rounded-xl border border-slate-700/50 p-6">
                        <h4 className="text-lg font-semibold text-slate-100 mb-4">关于UUID</h4>
                        <div className="space-y-3 text-sm text-slate-300">
                            <div>
                                <strong className="text-amber-400">UUID v4：</strong>
                                使用随机数生成，具有极低的重复概率（约1/5.3×10³⁶）
                            </div>
                            <div>
                                <strong className="text-orange-400">应用场景：</strong>
                                数据库主键、文件名、会话ID、分布式系统标识等
                            </div>
                            <div>
                                <strong className="text-slate-400">格式说明：</strong>
                                8-4-4-4-12位十六进制数字，总共36个字符（包含连字符）
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
