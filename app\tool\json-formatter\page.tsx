'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import ToolSidebar from '../../../components/ToolSidebar';
import ToastContainer from '../../../components/ToastContainer';
import { useToast } from '../../../hooks/useToast';

export default function JsonFormatterPage() {
    const router = useRouter();
    const [inputValue, setInputValue] = useState('');
    const [outputValue, setOutputValue] = useState('');
    const [error, setError] = useState('');
    const { toasts, showSuccess, showError, removeToast } = useToast();

    // 修复非标准JSON格式
    const fixNonStandardJson = (jsonStr: string): string => {
        let fixed = jsonStr.trim();

        // 移除注释
        fixed = fixed.replace(/\/\*[\s\S]*?\*\//g, '').replace(/\/\/.*$/gm, '');

        // 修复单引号为双引号
        fixed = fixed.replace(/'/g, '"');

        // 修复未加引号的键名
        fixed = fixed.replace(/([{,]\s*)([a-zA-Z_$][a-zA-Z0-9_$]*)\s*:/g, '$1"$2":');

        // 修复尾随逗号
        fixed = fixed.replace(/,(\s*[}\]])/g, '$1');

        // 修复undefined为null
        fixed = fixed.replace(/:\s*undefined/g, ': null');

        return fixed;
    };

    const formatJson = () => {
        try {
            setError('');
            let jsonStr = inputValue;

            // 首先尝试标准JSON解析
            try {
                const parsed = JSON.parse(jsonStr);
                setOutputValue(JSON.stringify(parsed, null, 2));
                return;
            } catch (e) {
                // 如果失败，尝试修复非标准JSON
                jsonStr = fixNonStandardJson(jsonStr);
                const parsed = JSON.parse(jsonStr);
                setOutputValue(JSON.stringify(parsed, null, 2));
                setError('⚠️ 已自动修复非标准JSON格式');
            }
        } catch (e) {
            setError('JSON格式错误: ' + (e as Error).message);
            setOutputValue('');
        }
    };

    const minifyJson = () => {
        try {
            setError('');
            let jsonStr = inputValue;

            try {
                const parsed = JSON.parse(jsonStr);
                setOutputValue(JSON.stringify(parsed));
                return;
            } catch (e) {
                jsonStr = fixNonStandardJson(jsonStr);
                const parsed = JSON.parse(jsonStr);
                setOutputValue(JSON.stringify(parsed));
                setError('⚠️ 已自动修复非标准JSON格式');
            }
        } catch (e) {
            setError('JSON格式错误: ' + (e as Error).message);
            setOutputValue('');
        }
    };

    const validateJson = () => {
        try {
            let jsonStr = inputValue;

            try {
                JSON.parse(jsonStr);
                setError('');
                setOutputValue('✅ JSON格式正确');
                return;
            } catch (e) {
                jsonStr = fixNonStandardJson(jsonStr);
                JSON.parse(jsonStr);
                setError('⚠️ 非标准JSON格式，但可以修复');
                setOutputValue('✅ 可修复的JSON格式');
            }
        } catch (e) {
            setError('❌ JSON格式错误: ' + (e as Error).message);
            setOutputValue('');
        }
    };

    const autoFixJson = () => {
        try {
            setError('');
            const fixed = fixNonStandardJson(inputValue);
            setInputValue(fixed);
            setOutputValue('✅ 已自动修复JSON格式');
        } catch (e) {
            setError('修复失败: ' + (e as Error).message);
        }
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-950 via-blue-950 to-indigo-950">
            {/* Fixed Header */}
            <header className="fixed top-0 left-0 right-0 z-50 bg-slate-950/90 backdrop-blur-md border-b border-cyan-500/20">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center h-16">
                        <div className="flex items-center">
                            <button
                                onClick={() => router.push('/')}
                                className="text-slate-400 hover:text-cyan-300 mr-4 transition-colors duration-300"
                            >
                                ← 返回
                            </button>
                            <h1 className="text-xl font-bold bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent">
                                JSON格式化工具
                            </h1>
                        </div>
                    </div>
                </div>
            </header>
            {/* Sidebar */}
            <ToolSidebar currentTool="jwt-tool" />
            <div className="pt-20 p-8">
                <div className="max-w-6xl mx-auto">
                    {/* Tool Header */}
                    <div className="mb-8">
                        <p className="text-slate-400 text-lg">格式化、压缩和验证JSON数据</p>
                    </div>

                    {/* Tool Interface */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        {/* Input Section */}
                        <div className="space-y-4">
                            <h3 className="text-lg font-semibold text-slate-100">输入JSON</h3>
                            <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-4">
                                <textarea
                                    value={inputValue}
                                    onChange={(e) => setInputValue(e.target.value)}
                                    placeholder='请输入JSON数据，例如：{"name": "张三", "age": 25}'
                                    className="w-full h-80 bg-transparent text-slate-100 placeholder-slate-500 resize-none focus:outline-none font-mono text-sm"
                                />
                            </div>
                            <div className="grid grid-cols-2 gap-3">
                                <button
                                    onClick={formatJson}
                                    className="py-3 px-6 rounded-lg bg-gradient-to-r from-cyan-400 to-blue-500 text-white font-semibold hover:shadow-lg hover:shadow-cyan-500/25 transition-all duration-300"
                                >
                                    格式化
                                </button>
                                <button
                                    onClick={minifyJson}
                                    className="py-3 px-6 rounded-lg bg-gradient-to-r from-slate-600 to-slate-700 text-white font-semibold hover:bg-gradient-to-r hover:from-slate-500 hover:to-slate-600 transition-all duration-300"
                                >
                                    压缩
                                </button>
                                <button
                                    onClick={validateJson}
                                    className="py-3 px-6 rounded-lg bg-gradient-to-r from-emerald-500 to-teal-600 text-white font-semibold hover:shadow-lg hover:shadow-emerald-500/25 transition-all duration-300"
                                >
                                    验证
                                </button>
                                <button
                                    onClick={autoFixJson}
                                    className="py-3 px-6 rounded-lg bg-gradient-to-r from-orange-500 to-red-600 text-white font-semibold hover:shadow-lg hover:shadow-orange-500/25 transition-all duration-300"
                                >
                                    自动修复
                                </button>
                            </div>
                        </div>

                        {/* Output Section */}
                        <div className="space-y-4">
                            <h3 className="text-lg font-semibold text-slate-100">输出结果</h3>
                            <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-4">
                                <textarea
                                    value={outputValue}
                                    readOnly
                                    placeholder="处理结果将显示在这里..."
                                    className="w-full h-80 bg-transparent text-slate-100 placeholder-slate-500 resize-none focus:outline-none font-mono text-sm"
                                />
                            </div>
                            {error && (
                                <div className="p-4 bg-red-900/50 border border-red-500/50 rounded-lg text-red-300 text-sm">
                                    {error}
                                </div>
                            )}
                            <button
                                onClick={() => navigator.clipboard.writeText(outputValue)}
                                disabled={!outputValue}
                                className="w-full py-3 px-6 rounded-lg bg-slate-700 text-white font-semibold hover:bg-slate-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-300"
                            >
                                复制结果
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
