'use client';

import { useRouter, usePathname } from 'next/navigation';
import { tools } from '@/app/toolsData';

interface Tool {
    name: string;
    desc: string;
    icon: string;
    key: string;
    color: string;
}

interface ToolSidebarProps {
    currentTool: string;
}

export default function ToolSidebar({ currentTool }: ToolSidebarProps) {
    const router = useRouter();
    const pathname = usePathname();

    return (
        <div className="w-80 bg-slate-900/50 backdrop-blur-md border-r border-slate-700/50 min-h-screen fixed left-0 top-16 z-30">
            <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                    <h2 className="text-lg font-semibold text-white">开发工具</h2>
                    <button
                        onClick={() => router.push('/')}
                        className="text-slate-400 hover:text-white transition-colors"
                        title="返回主页"
                    >
                        🏠
                    </button>
                </div>
                <div className="space-y-2 max-h-[calc(100vh-200px)] overflow-y-auto">
                    {tools.map((tool) => (
                        <button
                            key={tool.key}
                            onClick={() => router.push(`/tool/${tool.key}`)}
                            className={`w-full text-left p-3 rounded-lg transition-all duration-200 ${
                                tool.key === currentTool
                                    ? 'bg-gradient-to-r ' + tool.color + ' text-white shadow-lg'
                                    : 'text-slate-300 hover:bg-slate-800/50 hover:text-white'
                            }`}
                        >
                            <div className="flex items-center">
                                <span className="text-xl mr-3">{tool.icon}</span>
                                <div>
                                    <div className="font-medium">{tool.name}</div>
                                    <div className="text-xs opacity-75">{tool.desc}</div>
                                </div>
                            </div>
                        </button>
                    ))}
                </div>
            </div>
        </div>
    );
}
