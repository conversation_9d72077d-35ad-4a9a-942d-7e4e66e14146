'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';

export default function Base64EncoderPage() {
    const router = useRouter();
    const [inputValue, setInputValue] = useState('');
    const [outputValue, setOutputValue] = useState('');
    const [error, setError] = useState('');

    const encodeBase64 = () => {
        try {
            setError('');
            setOutputValue(btoa(unescape(encodeURIComponent(inputValue))));
        } catch (e) {
            setError('编码失败: ' + (e as Error).message);
            setOutputValue('');
        }
    };

    const decodeBase64 = () => {
        try {
            setError('');
            setOutputValue(decodeURIComponent(escape(atob(inputValue))));
        } catch (e) {
            setError('解码失败，请检查Base64格式是否正确');
            setOutputValue('');
        }
    };

    const encodeBase64Url = () => {
        try {
            setError('');
            const base64 = btoa(unescape(encodeURIComponent(inputValue)));
            const base64Url = base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
            setOutputValue(base64Url);
        } catch (e) {
            setError('编码失败: ' + (e as Error).message);
            setOutputValue('');
        }
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-950 via-blue-950 to-indigo-950">
            {/* Fixed Header */}
            <header className="fixed top-0 left-0 right-0 z-50 bg-slate-950/90 backdrop-blur-md border-b border-violet-500/20">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center h-16">
                        <div className="flex items-center">
                            <button
                                onClick={() => router.push('/')}
                                className="text-slate-400 hover:text-violet-300 mr-4 transition-colors duration-300"
                            >
                                ← 返回
                            </button>
                            <h1 className="text-xl font-bold bg-gradient-to-r from-violet-400 to-purple-400 bg-clip-text text-transparent">
                                Base64编解码工具
                            </h1>
                        </div>
                    </div>
                </div>
            </header>

            <div className="pt-20 p-8">
                <div className="max-w-6xl mx-auto">
                    {/* Tool Header */}
                    <div className="mb-8">
                        <p className="text-slate-400 text-lg">
                            支持文本的Base64编码解码，包括URL安全格式
                        </p>
                    </div>

                    {/* Tool Interface */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        {/* Input Section */}
                        <div className="space-y-4">
                            <h3 className="text-lg font-semibold text-slate-100">输入内容</h3>
                            <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-4">
                                <textarea
                                    value={inputValue}
                                    onChange={(e) => setInputValue(e.target.value)}
                                    placeholder="请输入需要编码的文本或Base64字符串进行解码"
                                    className="w-full h-80 bg-transparent text-slate-100 placeholder-slate-500 resize-none focus:outline-none text-sm font-mono"
                                />
                            </div>
                            <div className="grid grid-cols-1 gap-3">
                                <button
                                    onClick={encodeBase64}
                                    className="py-3 px-6 rounded-lg bg-gradient-to-r from-violet-400 to-purple-500 text-white font-semibold hover:shadow-lg hover:shadow-violet-500/25 transition-all duration-300"
                                >
                                    Base64编码
                                </button>
                                <button
                                    onClick={encodeBase64Url}
                                    className="py-3 px-6 rounded-lg bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold hover:shadow-lg hover:shadow-purple-500/25 transition-all duration-300"
                                >
                                    Base64 URL安全编码
                                </button>
                                <button
                                    onClick={decodeBase64}
                                    className="py-3 px-6 rounded-lg bg-gradient-to-r from-slate-600 to-slate-700 text-white font-semibold hover:bg-gradient-to-r hover:from-slate-500 hover:to-slate-600 transition-all duration-300"
                                >
                                    Base64解码
                                </button>
                            </div>
                        </div>

                        {/* Output Section */}
                        <div className="space-y-4">
                            <h3 className="text-lg font-semibold text-slate-100">输出结果</h3>
                            <div className="bg-slate-900/60 backdrop-blur-md rounded-xl border border-slate-700/50 p-4">
                                <textarea
                                    value={outputValue}
                                    readOnly
                                    placeholder="处理结果将显示在这里..."
                                    className="w-full h-80 bg-transparent text-slate-100 placeholder-slate-500 resize-none focus:outline-none text-sm font-mono"
                                />
                            </div>
                            {error && (
                                <div className="p-4 bg-red-900/50 border border-red-500/50 rounded-lg text-red-300 text-sm">
                                    {error}
                                </div>
                            )}
                            <button
                                onClick={() => navigator.clipboard.writeText(outputValue)}
                                disabled={!outputValue}
                                className="w-full py-3 px-6 rounded-lg bg-slate-700 text-white font-semibold hover:bg-slate-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-300"
                            >
                                复制结果
                            </button>
                        </div>
                    </div>

                    {/* Usage Examples */}
                    <div className="mt-12 bg-slate-900/40 backdrop-blur-md rounded-xl border border-slate-700/50 p-6">
                        <h4 className="text-lg font-semibold text-slate-100 mb-4">使用说明</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-slate-300">
                            <div>
                                <h5 className="font-medium text-violet-300 mb-2">标准Base64编码</h5>
                                <p>使用标准Base64字符集 (A-Z, a-z, 0-9, +, /)</p>
                                <p className="text-slate-400 mt-1">适用于：一般数据传输</p>
                            </div>
                            <div>
                                <h5 className="font-medium text-purple-300 mb-2">
                                    URL安全Base64编码
                                </h5>
                                <p>替换 + 为 -, / 为 _, 移除填充字符 =</p>
                                <p className="text-slate-400 mt-1">适用于：URL参数、文件名</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
